# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
dist/
typesense-data
app/artillery/reports
app/artillery/env
# Compiled Java class files
*.class
.vscode/

# Compiled Python bytecode
*.py[cod]
.vscode/

# Log files
*.log

# Package files
*.jar

# Maven
target/
dist/

# JetBrains IDE
.idea/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.tiff
*.avi
*.flv
*.mov
*.wmv
logs/
.next/
coverage/
.nyc_output/
audit.json

#public storybook
ui/public/storybook
storybook-static


# Local .terraform directories
**/.terraform/*
**/.terraform.*/**

# .tfstate files
*.tfstate
*.tfstate.*

# Crash log files
crash.log

# Ignore any .tfvars files that are generated automatically for each Terraform run. Most
# .tfvars files are managed as part of configuration and so should be included in
# version control.
#
# example.tfvars

# Ignore override files as they are usually used to override resources locally and so
# are not checked in
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Include override files you do wish to add to version control using negated pattern
#
# !example_override.tf

# Include tfplan files to ignore the plan output of command: terraform plan -out=tfplan
# example: *tfplan*

# macOS Files
.DS_Store


#deploy config
deploy.config.json

#tfvars
*.tfvars
