import React, { useState, useEffect } from 'react';
import moment from 'moment';
import DatePicker from '@/app/molecules/DatePicker';
import { Button } from '@/app/atoms';
import IconMinusCircle from '@/app/atoms/customIcons/IconMinusCircle.svg';
import TimeSlotComponent from './TimeSlotComponent';
import NextImage from 'next/image';

interface ExceptionItemProps {
    index: number;
    onRemove: () => void;
    register: any;
    control: any;
    setValue: (name: string, value: any) => void;
    watch: (name: string) => any;
    getValues: (name: string) => any;
    errors: any;
    selectedOption: string;
}

const ExceptionItem: React.FC<ExceptionItemProps> = ({
    index,
    onRemove,
    register,
    control,
    setValue,
    watch,
    getValues,
    errors,
    selectedOption,
}) => {
    const [showEndDate, setShowEndDate] = useState(false);
    const [updateTrigger, setUpdateTrigger] = useState(0);
    const [isInitialized, setIsInitialized] = useState(false);

    // Watch exception values for this index
    const startDate = watch(`exceptions.${index}.startDate`);
    const endDate = watch(`exceptions.${index}.endDate`);
    const exceptionTimes = watch(`exceptions.${index}.times`) || [];

    // Unique key for working hours state
    const workingHoursKey = `exception-${index}`;

    // Helper functions for time conversion
    const convertTo24HourFormat = (timeString: string) => {
        if (timeString === 'full-day') return 'full-day';
        return moment(timeString, 'h:mm A').format('HH:mm');
    };

    const convertTo12HourFormat = (timeString: string) => {
        if (timeString === 'full-day') return 'full-day';
        return moment(timeString, 'HH:mm').format('h:mm A');
    };

    // Initialize workingHours on mount if not set
    useEffect(() => {
        if (!isInitialized) {
            setShowEndDate(!!getValues(`exceptions.${index}.endDate`));

            const currentWorkingHours = getValues(
                `workingHours.${workingHoursKey}`
            );

            if (!currentWorkingHours || currentWorkingHours.length === 0) {
                if (exceptionTimes && exceptionTimes.length > 0) {
                    const formattedSlots = exceptionTimes.map((time: any) => ({
                        startTime:
                            (time.startTime || time.start) === 'full-day' ||
                            (time.startTime || time.start)?.includes('AM') ||
                            (time.startTime || time.start)?.includes('PM')
                                ? convertTo24HourFormat(
                                      time.startTime || time.start
                                  )
                                : time.startTime || time.start,
                        endTime:
                            (time.endTime || time.end) === 'full-day' ||
                            (time.endTime || time.end)?.includes('AM') ||
                            (time.endTime || time.end)?.includes('PM')
                                ? convertTo24HourFormat(
                                      time.endTime || time.end
                                  )
                                : time.endTime || time.end,
                        isWorkingDay: true,
                    }));
                    setValue(`workingHours.${workingHoursKey}`, formattedSlots);
                } else {
                    // Default slot if no times provided
                    setValue(`workingHours.${workingHoursKey}`, [
                        {
                            startTime: '09:00',
                            endTime: '17:00',
                            isWorkingDay: true,
                        },
                    ]);
                    setValue(`exceptions.${index}.times`, [
                        { start: '09:00', end: '17:00' },
                    ]);
                }
                setUpdateTrigger((prev) => prev + 1);
                setIsInitialized(true);
            }
        }
    }, [
        index,
        getValues,
        setValue,
        exceptionTimes,
        workingHoursKey,
        isInitialized,
    ]);

    const handleTimeSlotsChange = () => {
        const slots = getValues(`workingHours.${workingHoursKey}`) || [];
        const formattedTimes = slots.map((slot: any) => ({
            // Store both formats for compatibility
            start: slot.startTime,
            end: slot.endTime,
            startTime: slot.startTime,
            endTime: slot.endTime,
        }));
        setValue(`exceptions.${index}.times`, formattedTimes);
        setUpdateTrigger((prev) => prev + 1);
    };

    const addTimeSlot = () => {
        const currentSlots = getValues(`workingHours.${workingHoursKey}`) || [];
        // Prevent adding another slot if the only slot is full-day
        if (
            currentSlots.length === 1 &&
            currentSlots[0].startTime === 'full-day'
        )
            return;
        setValue(`workingHours.${workingHoursKey}`, [
            ...currentSlots,
            { startTime: '09:00', endTime: '17:00', isWorkingDay: true },
        ]);
        handleTimeSlotsChange();
    };

    const removeTimeSlot = (slotIndex: number) => {
        const currentSlots = getValues(`workingHours.${workingHoursKey}`) || [];
        // Allow removal only if more than one slot exists
        if (currentSlots.length > 1) {
            setValue(
                `workingHours.${workingHoursKey}`,
                currentSlots.filter((_: any, i: number) => i !== slotIndex)
            );
            handleTimeSlotsChange();
        }
    };

    const handleTimeChange = (
        slotIndex: number,
        field: 'startTime' | 'endTime',
        value: string
    ) => {
        const currentSlots = getValues(`workingHours.${workingHoursKey}`) || [];
        const updatedSlots = [...currentSlots];

        // Update the field with the provided value
        updatedSlots[slotIndex] = {
            ...updatedSlots[slotIndex],
            [field]: value,
        };

        // Handle special cases
        // 1. If we're changing startTime to '00:00' and endTime is '23:59', mark as full day
        if (
            field === 'startTime' &&
            value === '00:00' &&
            updatedSlots[slotIndex].endTime === '23:59'
        ) {
            // This is a full day, handled in TimeSlotComponent UI
            setValue(`exceptions.${index}.isFullDay`, true);
        }
        // 2. If we're changing endTime to '23:59' and startTime is '00:00', mark as full day
        else if (
            field === 'endTime' &&
            value === '23:59' &&
            updatedSlots[slotIndex].startTime === '00:00'
        ) {
            // This is a full day, handled in TimeSlotComponent UI
            setValue(`exceptions.${index}.isFullDay`, true);
        }
        // 3. Otherwise, not a full day
        else {
            setValue(`exceptions.${index}.isFullDay`, false);
        }

        setValue(`workingHours.${workingHoursKey}`, updatedSlots);
        handleTimeSlotsChange();
    };

    const handleRemoveEndDate = () => {
        setShowEndDate(false);
        setValue(`exceptions.${index}.endDate`, null);
    };

    return (
        <div>
            <div className="bg-[#F9F9F9] p-2 rounded-lg">
                <div className="flex flex-wrap items-center gap-2 mb-4">
                    {/* Start Date Section */}
                    <div className="flex items-center gap-1">
                        <label
                            htmlFor={`start-date-${index}`}
                            className="text-xs text-primary-900 select-none whitespace-nowrap"
                        >
                            Choose Date{' '}
                            <span className="text-red-500 ml-1">*</span>
                        </label>
                        <DatePicker
                            id={`start-date-${index}`}
                            name={`exceptions.${index}.startDate`}
                            value={startDate}
                            dateFormat="yyyy-MM-dd"
                            register={register}
                            errorMessage={
                                errors?.exceptions?.[index]?.startDate?.message
                            }
                            watch={watch}
                            variant="default"
                            onDateChange={(date) => {
                                setValue(
                                    `exceptions.${index}.startDate`,
                                    moment(date).format('YYYY-MM-DD')
                                );
                            }}
                            required={true}
                            minDate={new Date()}
                            inline={false}
                            placeholder="Select"
                            containerClass="z-50"
                        />
                    </div>

                    {/* End Date Section */}
                    {!showEndDate ? (
                        <button
                            type="button"
                            className="text-primary-700 text-sm font-medium flex items-center"
                            onClick={() => setShowEndDate(true)}
                        >
                            <span className="mr-1">+</span> End Date
                        </button>
                    ) : (
                        <div className="flex items-center gap-1">
                            <label
                                htmlFor={`end-date-${index}`}
                                className="text-xs text-primary-900 select-none whitespace-nowrap"
                            >
                                End Date
                            </label>
                            <DatePicker
                                id={`end-date-${index}`}
                                name={`exceptions.${index}.endDate`}
                                value={endDate}
                                dateFormat="yyyy-MM-dd"
                                register={register}
                                errorMessage={
                                    errors?.exceptions?.[index]?.endDate
                                        ?.message
                                }
                                watch={watch}
                                variant="default"
                                onDateChange={(date) => {
                                    setValue(
                                        `exceptions.${index}.endDate`,
                                        moment(date).format('YYYY-MM-DD')
                                    );
                                }}
                                required={false}
                                minDate={
                                    startDate ? new Date(startDate) : new Date()
                                }
                                inline={false}
                                placeholder="Select"
                                containerClass="z-50"
                            />

                            <Button
                                id={`remove-end-date-${index}`}
                                onClick={handleRemoveEndDate}
                                icon={<IconMinusCircle size={26} />}
                                onlyIcon={true}
                                size="mini"
                                variant="borderless"
                            />
                        </div>
                    )}
                </div>

                <div className="relative">
                    <div className="flex-1">
                        <TimeSlotComponent
                            key={`${workingHoursKey}-${updateTrigger}`}
                            slots={
                                getValues(`workingHours.${workingHoursKey}`) ||
                                []
                            }
                            onAddSlot={addTimeSlot}
                            onRemoveSlot={removeTimeSlot}
                            onTimeChange={handleTimeChange}
                            disabled={false}
                            className="mt-2"
                            selectedOption={selectedOption}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ExceptionItem;
