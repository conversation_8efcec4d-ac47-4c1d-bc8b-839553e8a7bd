import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import {
	Repository,
	DataSource,
	EntityManager,
	Between,
	LessThanOrEqual,
	<PERSON>T<PERSON><PERSON>r<PERSON>qual,
	<PERSON><PERSON>ull,
	Raw
} from 'typeorm';
import { AvailabilityService } from './availability.service';
import { ClinicAvailabilitySlot } from './entities/clinic-availability-slot.entity';
import { ClinicUser } from '../clinics/entities/clinic-user.entity';
import { ClinicEntity } from '../clinics/entities/clinic.entity';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { AppointmentDoctorsEntity } from '../appointments/entities/appointment-doctor.entity';
import {
	AvailabilityExceptionEntity,
	ExceptionType
} from '../users/entities/availability-exception.entity';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { SqsService } from '../utils/aws/sqs/sqs.service';

describe('AvailabilityService', () => {
	let service: AvailabilityService;
	let availabilityRepository: jest.Mocked<Repository<ClinicAvailabilitySlot>>;
	let clinicUserRepository: jest.Mocked<Repository<ClinicUser>>;
	let appointmentRepository: jest.Mocked<Repository<AppointmentEntity>>;
	let appointmentDoctorsRepository: jest.Mocked<
		Repository<AppointmentDoctorsEntity>
	>;
	let exceptionRepository: jest.Mocked<
		Repository<AvailabilityExceptionEntity>
	>;
	let logger: jest.Mocked<WinstonLogger>;
	let sqsService: jest.Mocked<SqsService>;
	let dataSource: jest.Mocked<DataSource>;
	let mockEntityManager: jest.Mocked<EntityManager>;

	const mockClinicUser: ClinicUser = {
		id: 'user-123',
		clinicId: 'clinic-123',
		userId: 'user-456',
		clinic: {
			id: 'clinic-123',
			timezone: 'Asia/Kolkata'
		} as ClinicEntity,
		workingHours: {
			workingHours: {
				monday: [
					{
						isWorkingDay: true,
						startTime: '09:00:00',
						endTime: '17:00:00'
					}
				],
				tuesday: [
					{
						isWorkingDay: false,
						startTime: null,
						endTime: null
					}
				],
				saturday: [
					{
						isWorkingDay: true,
						startTime: '10:00:00',
						endTime: '14:00:00'
					}
				]
			}
		}
	} as ClinicUser;

	const mockAvailabilitySlot: ClinicAvailabilitySlot = {
		id: 'slot-123',
		clinicUserId: 'user-123',
		date: '2023-01-01',
		startTime: new Date('2023-01-01T09:00:00.000Z'),
		endTime: new Date('2023-01-01T17:00:00.000Z'),
		isAvailable: true,
		createdAt: new Date(),
		updatedAt: new Date()
	} as ClinicAvailabilitySlot;

	const mockAppointmentDoctor: AppointmentDoctorsEntity = {
		id: 'ad-123',
		appointmentId: 'appointment-123',
		clinicUserId: 'user-123',
		doctorId: 'doctor-123',
		primary: true,
		createdAt: new Date(),
		updatedAt: new Date(),
		clinicUser: {} as ClinicUser,
		appointment: {
			id: 'appointment-123',
			startTime: new Date('2023-01-01T10:00:00.000Z'),
			endTime: new Date('2023-01-01T11:00:00.000Z'),
			date: new Date('2023-01-01')
		} as AppointmentEntity
	} as AppointmentDoctorsEntity;

	const mockException: AvailabilityExceptionEntity = {
		id: 'exception-123',
		clinicUserId: 'user-123',
		type: ExceptionType.OUT_OF_OFFICE,
		startDate: new Date('2023-01-01'),
		endDate: null,
		isFullDay: true,
		times: [],
		createdBy: 'user-123',
		updatedBy: 'user-123',
		createdAt: new Date(),
		updatedAt: new Date(),
		clinicUser: {} as ClinicUser
	} as AvailabilityExceptionEntity;

	const mockRepository = {
		find: jest.fn(),
		findOne: jest.fn(),
		save: jest.fn(),
		delete: jest.fn(),
		count: jest.fn(),
		createQueryBuilder: jest.fn(() => ({
			where: jest.fn().mockReturnThis(),
			andWhere: jest.fn().mockReturnThis(),
			getMany: jest.fn().mockResolvedValue([])
		}))
	};

	beforeEach(async () => {
		// Create mock entity manager
		mockEntityManager = {
			delete: jest.fn(),
			findOne: jest.fn(),
			find: jest.fn(),
			save: jest.fn(),
			remove: jest.fn(),
			createQueryBuilder: jest.fn(() => ({
				leftJoinAndSelect: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest.fn().mockResolvedValue([])
			})),
			getRepository: jest.fn()
		} as any;

		const module: TestingModule = await Test.createTestingModule({
			providers: [
				AvailabilityService,
				{
					provide: getRepositoryToken(ClinicAvailabilitySlot),
					useValue: {
						count: jest.fn(),
						find: jest.fn(),
						findOne: jest.fn(),
						save: jest.fn(),
						delete: jest.fn(),
						createQueryBuilder: jest.fn(() => ({
							where: jest.fn().mockReturnThis(),
							andWhere: jest.fn().mockReturnThis(),
							getMany: jest.fn().mockResolvedValue([])
						}))
					}
				},
				{
					provide: getRepositoryToken(ClinicUser),
					useValue: {
						findOne: jest.fn(),
						find: jest.fn(),
						createQueryBuilder: jest.fn()
					}
				},
				{
					provide: getRepositoryToken(AppointmentEntity),
					useValue: {
						find: jest.fn()
					}
				},
				{
					provide: getRepositoryToken(AppointmentDoctorsEntity),
					useValue: {
						find: jest.fn(),
						createQueryBuilder: jest.fn()
					}
				},
				{
					provide: getRepositoryToken(AvailabilityExceptionEntity),
					useValue: {
						find: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: {
						log: jest.fn(),
						error: jest.fn(),
						warn: jest.fn()
					}
				},
				{
					provide: SqsService,
					useValue: {
						sendMessage: jest.fn()
					}
				},
				{
					provide: DataSource,
					useValue: {
						transaction: jest.fn(),
						manager: {
							findOne: jest.fn()
						}
					}
				}
			]
		}).compile();

		service = module.get<AvailabilityService>(AvailabilityService);
		availabilityRepository = module.get(
			getRepositoryToken(ClinicAvailabilitySlot)
		);
		clinicUserRepository = module.get(getRepositoryToken(ClinicUser));
		appointmentRepository = module.get(
			getRepositoryToken(AppointmentEntity)
		);
		appointmentDoctorsRepository = module.get(
			getRepositoryToken(AppointmentDoctorsEntity)
		);
		exceptionRepository = module.get(
			getRepositoryToken(AvailabilityExceptionEntity)
		);
		logger = module.get(WinstonLogger);
		sqsService = module.get(SqsService);
		dataSource = module.get(DataSource);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it('should be defined', () => {
		expect(service).toBeDefined();
	});

	describe('generateSlotsForUser', () => {
		it('should be defined', () => {
			expect(service.generateSlotsForUser).toBeDefined();
		});

		it('should generate slots for a user successfully', async () => {
			const startDate = new Date('2023-01-01');
			const endDate = new Date('2023-01-02');

			// Mock transaction to call the callback with mockEntityManager
			dataSource.transaction.mockImplementation(async (callback: any) => {
				return await callback(mockEntityManager);
			});

			// Mock entity manager methods
			mockEntityManager.delete.mockResolvedValue({
				affected: 0,
				raw: {}
			});
			mockEntityManager.findOne.mockResolvedValue(mockClinicUser);
			mockEntityManager.find.mockResolvedValue([]); // No exceptions
			mockEntityManager.save.mockResolvedValue(mockAvailabilitySlot);
			mockEntityManager.createQueryBuilder.mockReturnValue({
				leftJoinAndSelect: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest.fn().mockResolvedValue([]) // No appointments
			} as any);

			await service.generateSlotsForUser('user-123', startDate, endDate);

			expect(dataSource.transaction).toHaveBeenCalled();
			expect(mockEntityManager.delete).toHaveBeenCalledWith(
				ClinicAvailabilitySlot,
				{
					clinicUserId: 'user-123',
					date: Between('2023-01-01', '2023-01-02')
				}
			);
			expect(mockEntityManager.findOne).toHaveBeenCalledWith(ClinicUser, {
				where: { id: 'user-123' },
				relations: ['clinic']
			});
		});

		it('should handle clinic user not found', async () => {
			const startDate = new Date('2023-01-01');
			const endDate = new Date('2023-01-02');

			dataSource.transaction.mockImplementation(async (callback: any) => {
				return await callback(mockEntityManager);
			});

			mockEntityManager.delete.mockResolvedValue({
				affected: 0,
				raw: {}
			});
			mockEntityManager.findOne.mockResolvedValue(null); // User not found

			await service.generateSlotsForUser('user-123', startDate, endDate);

			expect(logger.error).toHaveBeenCalledWith(
				'Cannot generate slots: Clinic user user-123 not found'
			);
		});

		it('should use default UTC timezone when clinic timezone is missing', async () => {
			const startDate = new Date('2023-01-01');
			const endDate = new Date('2023-01-02');
			const userWithoutTimezone = {
				...mockClinicUser,
				clinic: { id: 'clinic-123', timezone: null }
			} as any;

			dataSource.transaction.mockImplementation(async (callback: any) => {
				return await callback(mockEntityManager);
			});

			mockEntityManager.delete.mockResolvedValue({
				affected: 0,
				raw: {}
			});
			mockEntityManager.findOne.mockResolvedValue(userWithoutTimezone);
			mockEntityManager.find.mockResolvedValue([]);
			mockEntityManager.createQueryBuilder.mockReturnValue({
				leftJoinAndSelect: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest.fn().mockResolvedValue([])
			} as any);

			await service.generateSlotsForUser('user-123', startDate, endDate);

			expect(logger.log).toHaveBeenCalledWith(
				'Using clinic timezone',
				expect.objectContaining({
					timezone: 'UTC'
				})
			);
		});

		it('should handle missing working hours configuration', async () => {
			const startDate = new Date('2023-01-01');
			const endDate = new Date('2023-01-02');
			const userWithoutWorkingHours = {
				...mockClinicUser,
				workingHours: null
			} as ClinicUser;

			dataSource.transaction.mockImplementation(async (callback: any) => {
				return await callback(mockEntityManager);
			});

			mockEntityManager.delete.mockResolvedValue({
				affected: 0,
				raw: {}
			});
			mockEntityManager.findOne.mockResolvedValue(
				userWithoutWorkingHours
			);
			mockEntityManager.find.mockResolvedValue([]);
			mockEntityManager.createQueryBuilder.mockReturnValue({
				leftJoinAndSelect: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest.fn().mockResolvedValue([])
			} as any);

			await service.generateSlotsForUser('user-123', startDate, endDate);

			expect(logger.warn).toHaveBeenCalledWith(
				'Missing working hours configuration',
				expect.objectContaining({
					clinicUserId: 'user-123'
				})
			);
		});

		it('should process Saturday working hours correctly', async () => {
			const startDate = new Date('2023-01-07'); // Saturday
			const endDate = new Date('2023-01-07');

			dataSource.transaction.mockImplementation(async (callback: any) => {
				return await callback(mockEntityManager);
			});

			mockEntityManager.delete.mockResolvedValue({
				affected: 0,
				raw: {}
			});
			mockEntityManager.findOne.mockResolvedValue(mockClinicUser);
			mockEntityManager.find.mockResolvedValue([]);
			mockEntityManager.save.mockResolvedValue(mockAvailabilitySlot);
			mockEntityManager.createQueryBuilder.mockReturnValue({
				leftJoinAndSelect: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest.fn().mockResolvedValue([])
			} as any);

			await service.generateSlotsForUser('user-123', startDate, endDate);

			expect(logger.log).toHaveBeenCalledWith(
				'Processing Saturday',
				expect.objectContaining({
					dayOfWeek: 'saturday'
				})
			);
		});

		it('should skip non-working days', async () => {
			const startDate = new Date('2023-01-03'); // Tuesday (non-working day in mock)
			const endDate = new Date('2023-01-03');

			dataSource.transaction.mockImplementation(async (callback: any) => {
				return await callback(mockEntityManager);
			});

			mockEntityManager.delete.mockResolvedValue({
				affected: 0,
				raw: {}
			});
			mockEntityManager.findOne.mockResolvedValue(mockClinicUser);
			mockEntityManager.find.mockResolvedValue([]);
			mockEntityManager.createQueryBuilder.mockReturnValue({
				leftJoinAndSelect: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest.fn().mockResolvedValue([])
			} as any);

			await service.generateSlotsForUser('user-123', startDate, endDate);

			expect(logger.log).toHaveBeenCalledWith(
				'Skipping non-working day',
				expect.objectContaining({
					dayOfWeek: 'tuesday'
				})
			);
		});

		it('should handle invalid working hours (null times)', async () => {
			const startDate = new Date('2023-01-02'); // Monday
			const endDate = new Date('2023-01-02');
			const userWithNullTimes = {
				...mockClinicUser,
				workingHours: {
					workingHours: {
						monday: [
							{
								isWorkingDay: true,
								startTime: null,
								endTime: null
							}
						]
					}
				}
			} as ClinicUser;

			dataSource.transaction.mockImplementation(async (callback: any) => {
				return await callback(mockEntityManager);
			});

			mockEntityManager.delete.mockResolvedValue({
				affected: 0,
				raw: {}
			});
			mockEntityManager.findOne.mockResolvedValue(userWithNullTimes);
			mockEntityManager.find.mockResolvedValue([]);
			mockEntityManager.createQueryBuilder.mockReturnValue({
				leftJoinAndSelect: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest.fn().mockResolvedValue([])
			} as any);

			await service.generateSlotsForUser('user-123', startDate, endDate);

			expect(logger.log).toHaveBeenCalledWith(
				'Skipping invalid working hours (missing or null times)',
				expect.objectContaining({
					dayOfWeek: 'monday'
				})
			);
		});

		it('should handle invalid time ranges (end before start)', async () => {
			const startDate = new Date('2023-01-02'); // Monday
			const endDate = new Date('2023-01-02');
			const userWithInvalidRange = {
				...mockClinicUser,
				workingHours: {
					workingHours: {
						monday: [
							{
								isWorkingDay: true,
								startTime: '17:00:00',
								endTime: '09:00:00' // End before start
							}
						]
					}
				}
			} as ClinicUser;

			dataSource.transaction.mockImplementation(async (callback: any) => {
				return await callback(mockEntityManager);
			});

			mockEntityManager.delete.mockResolvedValue({
				affected: 0,
				raw: {}
			});
			mockEntityManager.findOne.mockResolvedValue(userWithInvalidRange);
			mockEntityManager.find.mockResolvedValue([]);
			mockEntityManager.createQueryBuilder.mockReturnValue({
				leftJoinAndSelect: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest.fn().mockResolvedValue([])
			} as any);

			await service.generateSlotsForUser('user-123', startDate, endDate);

			expect(logger.warn).toHaveBeenCalledWith(
				'Invalid working hour range: end time is not after start time',
				expect.objectContaining({
					clinicUserId: 'user-123'
				})
			);
		});

		it('should process OUT_OF_OFFICE full day exception', async () => {
			const startDate = new Date('2023-01-01');
			const endDate = new Date('2023-01-01');
			const fullDayException = {
				...mockException,
				type: ExceptionType.OUT_OF_OFFICE,
				isFullDay: true
			};

			dataSource.transaction.mockImplementation(async (callback: any) => {
				return await callback(mockEntityManager);
			});

			mockEntityManager.delete.mockResolvedValue({
				affected: 0,
				raw: {}
			});
			mockEntityManager.findOne.mockResolvedValue(mockClinicUser);
			mockEntityManager.find.mockResolvedValue([fullDayException]);
			mockEntityManager.createQueryBuilder.mockReturnValue({
				leftJoinAndSelect: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest.fn().mockResolvedValue([])
			} as any);

			await service.generateSlotsForUser('user-123', startDate, endDate);

			expect(logger.log).toHaveBeenCalledWith(
				'Applying full-day OUT_OF_OFFICE exception',
				expect.objectContaining({
					exceptionId: 'exception-123'
				})
			);
		});

		it('should process ADDITIONAL_HOURS exception', async () => {
			const startDate = new Date('2023-01-01');
			const endDate = new Date('2023-01-01');
			const additionalHoursException = {
				...mockException,
				type: ExceptionType.ADDITIONAL_HOURS,
				isFullDay: false,
				times: [
					{
						startTime: '18:00:00',
						endTime: '20:00:00'
					}
				]
			};

			dataSource.transaction.mockImplementation(async (callback: any) => {
				return await callback(mockEntityManager);
			});

			mockEntityManager.delete.mockResolvedValue({
				affected: 0,
				raw: {}
			});
			mockEntityManager.findOne.mockResolvedValue(mockClinicUser);
			mockEntityManager.find.mockResolvedValue([
				additionalHoursException
			]);
			mockEntityManager.save.mockResolvedValue(mockAvailabilitySlot);
			mockEntityManager.createQueryBuilder.mockReturnValue({
				leftJoinAndSelect: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest.fn().mockResolvedValue([])
			} as any);

			await service.generateSlotsForUser('user-123', startDate, endDate);

			expect(logger.log).toHaveBeenCalledWith(
				'Applying ADDITIONAL_HOURS exception',
				expect.objectContaining({
					exceptionId: 'exception-123'
				})
			);
		});

		it('should process appointments and remove appointment times', async () => {
			const startDate = new Date('2023-01-01');
			const endDate = new Date('2023-01-01');

			dataSource.transaction.mockImplementation(async (callback: any) => {
				return await callback(mockEntityManager);
			});

			mockEntityManager.delete.mockResolvedValue({
				affected: 0,
				raw: {}
			});
			mockEntityManager.findOne.mockResolvedValue(mockClinicUser);
			mockEntityManager.find.mockResolvedValue([]);
			mockEntityManager.save.mockResolvedValue(mockAvailabilitySlot);
			mockEntityManager.createQueryBuilder.mockReturnValue({
				leftJoinAndSelect: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest.fn().mockResolvedValue([mockAppointmentDoctor])
			} as any);

			await service.generateSlotsForUser('user-123', startDate, endDate);

			expect(logger.log).toHaveBeenCalledWith(
				'Processing appointment (using UTC from DB)',
				expect.objectContaining({
					appointmentId: 'appointment-123'
				})
			);
		});

		it('should handle invalid appointment times', async () => {
			const startDate = new Date('2023-01-01');
			const endDate = new Date('2023-01-01');
			const invalidAppointment = {
				...mockAppointmentDoctor,
				appointment: {
					...mockAppointmentDoctor.appointment,
					startTime: 'invalid-date',
					endTime: 'invalid-date'
				}
			};

			dataSource.transaction.mockImplementation(async (callback: any) => {
				return await callback(mockEntityManager);
			});

			mockEntityManager.delete.mockResolvedValue({
				affected: 0,
				raw: {}
			});
			mockEntityManager.findOne.mockResolvedValue(mockClinicUser);
			mockEntityManager.find.mockResolvedValue([]);
			mockEntityManager.createQueryBuilder.mockReturnValue({
				leftJoinAndSelect: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest.fn().mockResolvedValue([invalidAppointment])
			} as any);

			await service.generateSlotsForUser('user-123', startDate, endDate);

			expect(logger.error).toHaveBeenCalledWith(
				'Invalid appointment date/time from database',
				expect.objectContaining({
					appointmentId: 'appointment-123'
				})
			);
		});

		it('should perform defragmentation when multiple slots exist', async () => {
			const startDate = new Date('2023-01-01');
			const endDate = new Date('2023-01-01');

			dataSource.transaction.mockImplementation(async (callback: any) => {
				return await callback(mockEntityManager);
			});

			mockEntityManager.delete.mockResolvedValue({
				affected: 0,
				raw: {}
			});
			mockEntityManager.findOne.mockResolvedValue(mockClinicUser);
			mockEntityManager.find.mockResolvedValue([]); // No exceptions
			mockEntityManager.save.mockResolvedValue(mockAvailabilitySlot);
			mockEntityManager.createQueryBuilder.mockReturnValue({
				leftJoinAndSelect: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest.fn().mockResolvedValue([])
			} as any);

			await service.generateSlotsForUser('user-123', startDate, endDate);

			// Since defragmentation logic is complex and depends on internal implementation,
			// we'll just verify the method completes successfully
			expect(dataSource.transaction).toHaveBeenCalled();
			expect(mockEntityManager.findOne).toHaveBeenCalled();
		});
	});

	describe('checkUserAvailability', () => {
		it('should be defined', () => {
			expect(service.checkUserAvailability).toBeDefined();
		});

		it('should return true when user is available', async () => {
			clinicUserRepository.findOne.mockResolvedValue(mockClinicUser);
			availabilityRepository.count.mockResolvedValue(1);

			const result = await service.checkUserAvailability(
				'user-123',
				'2023-01-01',
				'09:00:00',
				'10:00:00'
			);

			expect(result).toBe(true);
			expect(availabilityRepository.count).toHaveBeenCalledWith({
				where: {
					clinicUserId: 'user-123',
					date: '2023-01-01',
					startTime: LessThanOrEqual(expect.any(Date)),
					endTime: MoreThanOrEqual(expect.any(Date)),
					isAvailable: true
				}
			});
		});

		it('should return false when user is not available', async () => {
			clinicUserRepository.findOne.mockResolvedValue(mockClinicUser);
			availabilityRepository.count.mockResolvedValue(0);

			const result = await service.checkUserAvailability(
				'user-123',
				'2023-01-01',
				'09:00:00',
				'10:00:00'
			);

			expect(result).toBe(false);
		});

		it('should return false for invalid date format', async () => {
			const result = await service.checkUserAvailability(
				'user-123',
				'invalid-date',
				'09:00:00',
				'10:00:00'
			);

			expect(result).toBe(false);
			expect(logger.error).toHaveBeenCalledWith(
				'Invalid date or time format in checkUserAvailability',
				expect.objectContaining({
					date: 'invalid-date'
				})
			);
		});

		it('should return false for invalid time format', async () => {
			const result = await service.checkUserAvailability(
				'user-123',
				'2023-01-01',
				'invalid-time',
				'10:00:00'
			);

			expect(result).toBe(false);
			expect(logger.error).toHaveBeenCalledWith(
				'Invalid date or time format in checkUserAvailability',
				expect.objectContaining({
					startTime: 'invalid-time'
				})
			);
		});

		it('should return false when clinic user not found', async () => {
			clinicUserRepository.findOne.mockResolvedValue(null);

			const result = await service.checkUserAvailability(
				'user-123',
				'2023-01-01',
				'09:00:00',
				'10:00:00'
			);

			expect(result).toBe(false);
			expect(logger.error).toHaveBeenCalledWith(
				'Clinic user or clinic not found',
				expect.objectContaining({
					clinicUserId: 'user-123'
				})
			);
		});

		it('should return false when clinic not found', async () => {
			const userWithoutClinic = {
				...mockClinicUser,
				clinic: null
			} as any;
			clinicUserRepository.findOne.mockResolvedValue(userWithoutClinic);

			const result = await service.checkUserAvailability(
				'user-123',
				'2023-01-01',
				'09:00:00',
				'10:00:00'
			);

			expect(result).toBe(false);
			expect(logger.error).toHaveBeenCalledWith(
				'Clinic user or clinic not found',
				expect.objectContaining({
					clinicUserId: 'user-123'
				})
			);
		});

		it('should handle UTC time strings with Z suffix', async () => {
			clinicUserRepository.findOne.mockResolvedValue(mockClinicUser);
			availabilityRepository.count.mockResolvedValue(1);

			const result = await service.checkUserAvailability(
				'user-123',
				'2023-01-01',
				'2023-01-01T09:00:00.000Z',
				'2023-01-01T10:00:00.000Z'
			);

			expect(result).toBe(true);
			expect(logger.log).toHaveBeenCalledWith(
				'Input times already contain Z suffix, using as UTC',
				expect.objectContaining({
					startTime: '2023-01-01T09:00:00.000Z'
				})
			);
		});

		it('should return false for invalid date objects', async () => {
			clinicUserRepository.findOne.mockResolvedValue(mockClinicUser);

			// Test with invalid input that will create invalid dates
			const result = await service.checkUserAvailability(
				'user-123',
				'2023-01-01',
				'invalid-time',
				'10:00:00'
			);

			expect(result).toBe(false);
			expect(logger.error).toHaveBeenCalledWith(
				'Invalid date or time format in checkUserAvailability',
				expect.objectContaining({
					startTime: 'invalid-time'
				})
			);
		});
	});

	describe('findAvailableUsers', () => {
		it('should be defined', () => {
			expect(service.findAvailableUsers).toBeDefined();
		});

		it('should return available user IDs', async () => {
			const mockClinic = {
				id: 'clinic-123',
				timezone: 'Asia/Kolkata'
			} as ClinicEntity;
			const mockQueryBuilder = {
				createQueryBuilder: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				innerJoin: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest.fn().mockResolvedValue([mockClinicUser])
			};

			// Mock dataSource.manager.findOne for clinic
			dataSource.manager.findOne = jest
				.fn()
				.mockResolvedValue(mockClinic);

			// Mock clinicUserRepository.createQueryBuilder
			clinicUserRepository.createQueryBuilder.mockReturnValue(
				mockQueryBuilder as any
			);

			// Mock availabilityRepository.createQueryBuilder for the service's internal query
			availabilityRepository.createQueryBuilder.mockReturnValue({
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest
					.fn()
					.mockResolvedValue([
						{ ...mockAvailabilitySlot, clinicUserId: 'user-123' }
					])
			} as any);

			const result = await service.findAvailableUsers(
				'clinic-123',
				'2023-01-01',
				'09:00:00',
				'10:00:00'
			);

			expect(result).toEqual(['user-123']);
			expect(dataSource.manager.findOne).toHaveBeenCalledWith(
				ClinicEntity,
				{
					where: { id: 'clinic-123' }
				}
			);
		});

		it('should return empty array for invalid date format', async () => {
			const result = await service.findAvailableUsers(
				'clinic-123',
				'invalid-date',
				'09:00:00',
				'10:00:00'
			);

			expect(result).toEqual([]);
			expect(logger.error).toHaveBeenCalledWith(
				'Invalid date or time format in findAvailableUsers',
				expect.objectContaining({
					date: 'invalid-date'
				})
			);
		});

		it('should return empty array when clinic not found', async () => {
			dataSource.manager.findOne = jest.fn().mockResolvedValue(null);

			const result = await service.findAvailableUsers(
				'clinic-123',
				'2023-01-01',
				'09:00:00',
				'10:00:00'
			);

			expect(result).toEqual([]);
			expect(logger.error).toHaveBeenCalledWith(
				'Clinic not found in findAvailableUsers',
				expect.objectContaining({
					clinicId: 'clinic-123'
				})
			);
		});

		it('should filter by role when provided', async () => {
			const mockClinic = {
				id: 'clinic-123',
				timezone: 'Asia/Kolkata'
			} as ClinicEntity;
			const mockQueryBuilder = {
				createQueryBuilder: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				innerJoin: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest.fn().mockResolvedValue([mockClinicUser])
			};

			dataSource.manager.findOne = jest
				.fn()
				.mockResolvedValue(mockClinic);
			clinicUserRepository.createQueryBuilder.mockReturnValue(
				mockQueryBuilder as any
			);
			availabilityRepository.createQueryBuilder.mockReturnValue({
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest
					.fn()
					.mockResolvedValue([
						{ ...mockAvailabilitySlot, clinicUserId: 'user-123' }
					])
			} as any);

			const result = await service.findAvailableUsers(
				'clinic-123',
				'2023-01-01',
				'09:00:00',
				'10:00:00',
				{ role: 'doctor' }
			);

			expect(result).toEqual(['user-123']);
			expect(mockQueryBuilder.innerJoin).toHaveBeenCalledWith(
				'cu.user',
				'u'
			);
			expect(mockQueryBuilder.innerJoin).toHaveBeenCalledWith(
				'u.role',
				'r'
			);
			expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
				'r.name = :role',
				{ role: 'doctor' }
			);
		});

		it('should filter by specialty when provided', async () => {
			const mockClinic = {
				id: 'clinic-123',
				timezone: 'Asia/Kolkata'
			} as ClinicEntity;
			const mockQueryBuilder = {
				createQueryBuilder: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				innerJoin: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest.fn().mockResolvedValue([mockClinicUser])
			};

			dataSource.manager.findOne = jest
				.fn()
				.mockResolvedValue(mockClinic);
			clinicUserRepository.createQueryBuilder.mockReturnValue(
				mockQueryBuilder as any
			);
			availabilityRepository.createQueryBuilder.mockReturnValue({
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest
					.fn()
					.mockResolvedValue([
						{ ...mockAvailabilitySlot, clinicUserId: 'user-123' }
					])
			} as any);

			const result = await service.findAvailableUsers(
				'clinic-123',
				'2023-01-01',
				'09:00:00',
				'10:00:00',
				{ specialty: 'cardiology' }
			);

			expect(result).toEqual(['user-123']);
			expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
				'cu.specialties @> ARRAY[:specialty]::varchar[]',
				{ specialty: 'cardiology' }
			);
		});

		it('should return empty array when no clinic users match filters', async () => {
			const mockClinic = {
				id: 'clinic-123',
				timezone: 'Asia/Kolkata'
			} as ClinicEntity;
			const mockQueryBuilder = {
				createQueryBuilder: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				innerJoin: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest.fn().mockResolvedValue([]) // No users found
			};

			dataSource.manager.findOne = jest
				.fn()
				.mockResolvedValue(mockClinic);
			clinicUserRepository.createQueryBuilder.mockReturnValue(
				mockQueryBuilder as any
			);

			const result = await service.findAvailableUsers(
				'clinic-123',
				'2023-01-01',
				'09:00:00',
				'10:00:00'
			);

			expect(result).toEqual([]);
			expect(logger.log).toHaveBeenCalledWith(
				'No clinic users found matching filters',
				expect.objectContaining({
					clinicId: 'clinic-123'
				})
			);
		});

		it('should handle UTC time strings with Z suffix', async () => {
			const mockClinic = {
				id: 'clinic-123',
				timezone: 'Asia/Kolkata'
			} as ClinicEntity;
			const mockQueryBuilder = {
				createQueryBuilder: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				innerJoin: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest.fn().mockResolvedValue([mockClinicUser])
			};

			dataSource.manager.findOne = jest
				.fn()
				.mockResolvedValue(mockClinic);
			clinicUserRepository.createQueryBuilder.mockReturnValue(
				mockQueryBuilder as any
			);
			availabilityRepository.createQueryBuilder.mockReturnValue({
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest
					.fn()
					.mockResolvedValue([
						{ ...mockAvailabilitySlot, clinicUserId: 'user-123' }
					])
			} as any);

			const result = await service.findAvailableUsers(
				'clinic-123',
				'2023-01-01',
				'2023-01-01T09:00:00.000Z',
				'2023-01-01T10:00:00.000Z'
			);

			expect(result).toEqual(['user-123']);
			expect(logger.log).toHaveBeenCalledWith(
				'Input times already contain Z suffix, using as UTC',
				expect.objectContaining({
					startTime: '2023-01-01T09:00:00.000Z'
				})
			);
		});
	});

	describe('Private method tests', () => {
		describe('isValidDateFormat', () => {
			it('should validate YYYY-MM-DD format', () => {
				const isValid = (service as any).isValidDateFormat(
					'2023-01-01'
				);
				expect(isValid).toBe(true);
			});

			it('should validate ISO-8601 format', () => {
				const isValid = (service as any).isValidDateFormat(
					'2023-01-01T10:00:00.000Z'
				);
				expect(isValid).toBe(true);
			});

			it('should reject invalid formats', () => {
				const isValid = (service as any).isValidDateFormat(
					'invalid-date'
				);
				expect(isValid).toBe(false);
			});

			it('should reject empty string', () => {
				const isValid = (service as any).isValidDateFormat('');
				expect(isValid).toBe(false);
			});

			it('should reject null/undefined', () => {
				const isValid1 = (service as any).isValidDateFormat(null);
				const isValid2 = (service as any).isValidDateFormat(undefined);
				expect(isValid1).toBe(false);
				expect(isValid2).toBe(false);
			});
		});

		describe('isValidTimeFormat', () => {
			it('should validate HH:MM:SS format', () => {
				const isValid = (service as any).isValidTimeFormat('10:30:00');
				expect(isValid).toBe(true);
			});

			it('should validate HH:MM format', () => {
				const isValid = (service as any).isValidTimeFormat('10:30');
				expect(isValid).toBe(true);
			});

			it('should validate ISO-8601 format', () => {
				const isValid = (service as any).isValidTimeFormat(
					'2023-01-01T10:30:00.000Z'
				);
				expect(isValid).toBe(true);
			});

			it('should reject invalid formats', () => {
				const isValid = (service as any).isValidTimeFormat(
					'invalid-time'
				);
				expect(isValid).toBe(false);
			});

			it('should reject empty string', () => {
				const isValid = (service as any).isValidTimeFormat('');
				expect(isValid).toBe(false);
			});
		});

		describe('getTimezoneOffset', () => {
			it('should calculate timezone offset correctly', () => {
				const offset = (service as any).getTimezoneOffset(
					'Asia/Kolkata'
				);
				expect(typeof offset).toBe('number');
			});

			it('should handle invalid timezone gracefully', () => {
				const offset = (service as any).getTimezoneOffset(
					'Invalid/Timezone'
				);
				expect(offset).toBe(0);
				expect(logger.error).toHaveBeenCalled();
			});
		});

		describe('localToUtc', () => {
			it('should convert local time to UTC', () => {
				const utcDate = (service as any).localToUtc(
					'2023-01-01T10:00:00',
					'Asia/Kolkata'
				);
				expect(utcDate).toBeInstanceOf(Date);
			});

			it('should handle UTC strings with Z suffix', () => {
				const utcDate = (service as any).localToUtc(
					'2023-01-01T10:00:00.000Z',
					'Asia/Kolkata'
				);
				expect(utcDate).toBeInstanceOf(Date);
				expect(logger.log).toHaveBeenCalledWith(
					'Input string already in UTC format, no conversion needed',
					expect.objectContaining({
						isUTC: true
					})
				);
			});

			it('should handle conversion errors gracefully', () => {
				const utcDate = (service as any).localToUtc(
					'invalid-date',
					'Asia/Kolkata'
				);
				expect(utcDate).toBeInstanceOf(Date);
				expect(logger.error).toHaveBeenCalled();
			});
		});

		describe('removeAppointmentTime', () => {
			it('should remove appointment time from slots', () => {
				const slots = [
					{
						start: new Date('2023-01-01T09:00:00.000Z'),
						end: new Date('2023-01-01T17:00:00.000Z')
					}
				];
				const appointmentStart = new Date('2023-01-01T10:00:00.000Z');
				const appointmentEnd = new Date('2023-01-01T11:00:00.000Z');

				const result = (service as any).removeAppointmentTime(
					slots,
					appointmentStart,
					appointmentEnd
				);

				expect(result).toHaveLength(2); // Should split into two slots
				expect(result[0].end).toEqual(appointmentStart);
				expect(result[1].start).toEqual(appointmentEnd);
			});

			it('should handle appointment at start of slot', () => {
				const slots = [
					{
						start: new Date('2023-01-01T09:00:00.000Z'),
						end: new Date('2023-01-01T17:00:00.000Z')
					}
				];
				const appointmentStart = new Date('2023-01-01T09:00:00.000Z');
				const appointmentEnd = new Date('2023-01-01T10:00:00.000Z');

				const result = (service as any).removeAppointmentTime(
					slots,
					appointmentStart,
					appointmentEnd
				);

				expect(result).toHaveLength(1); // Should have one remaining slot
				expect(result[0].start).toEqual(appointmentEnd);
			});

			it('should handle appointment at end of slot', () => {
				const slots = [
					{
						start: new Date('2023-01-01T09:00:00.000Z'),
						end: new Date('2023-01-01T17:00:00.000Z')
					}
				];
				const appointmentStart = new Date('2023-01-01T16:00:00.000Z');
				const appointmentEnd = new Date('2023-01-01T17:00:00.000Z');

				const result = (service as any).removeAppointmentTime(
					slots,
					appointmentStart,
					appointmentEnd
				);

				expect(result).toHaveLength(1); // Should have one remaining slot
				expect(result[0].end).toEqual(appointmentStart);
			});

			it('should remove entire slot if appointment covers it completely', () => {
				const slots = [
					{
						start: new Date('2023-01-01T10:00:00.000Z'),
						end: new Date('2023-01-01T11:00:00.000Z')
					}
				];
				const appointmentStart = new Date('2023-01-01T09:00:00.000Z');
				const appointmentEnd = new Date('2023-01-01T12:00:00.000Z');

				const result = (service as any).removeAppointmentTime(
					slots,
					appointmentStart,
					appointmentEnd
				);

				expect(result).toHaveLength(0); // Should remove the entire slot
			});
		});
	});

	describe('Error handling', () => {
		it('should handle transaction errors in generateSlotsForUser', async () => {
			const startDate = new Date('2023-01-01');
			const endDate = new Date('2023-01-02');
			const transactionError = new Error('Transaction failed');

			dataSource.transaction.mockRejectedValue(transactionError);

			await expect(
				service.generateSlotsForUser('user-123', startDate, endDate)
			).rejects.toThrow('Transaction failed');
		});

		it('should handle repository errors in checkUserAvailability', async () => {
			const repositoryError = new Error('Repository error');
			clinicUserRepository.findOne.mockRejectedValue(repositoryError);

			await expect(
				service.checkUserAvailability(
					'user-123',
					'2023-01-01',
					'09:00:00',
					'10:00:00'
				)
			).rejects.toThrow('Repository error');
		});

		it('should handle repository errors in findAvailableUsers', async () => {
			const repositoryError = new Error('Repository error');
			dataSource.manager.findOne = jest
				.fn()
				.mockRejectedValue(repositoryError);

			await expect(
				service.findAvailableUsers(
					'clinic-123',
					'2023-01-01',
					'09:00:00',
					'10:00:00'
				)
			).rejects.toThrow('Repository error');
		});
	});

	describe('Additional coverage tests', () => {
		describe('Constructor edge cases', () => {
			it('should log warning when SqsService is not available', async () => {
				// Create a new service instance without SqsService
				const moduleWithoutSqs: TestingModule =
					await Test.createTestingModule({
						providers: [
							AvailabilityService,
							{
								provide: getRepositoryToken(
									ClinicAvailabilitySlot
								),
								useValue: mockRepository
							},
							{
								provide: getRepositoryToken(ClinicUser),
								useValue: mockRepository
							},
							{
								provide: getRepositoryToken(AppointmentEntity),
								useValue: mockRepository
							},
							{
								provide: getRepositoryToken(
									AppointmentDoctorsEntity
								),
								useValue: mockRepository
							},
							{
								provide: getRepositoryToken(
									AvailabilityExceptionEntity
								),
								useValue: mockRepository
							},
							{
								provide: WinstonLogger,
								useValue: {
									log: jest.fn(),
									error: jest.fn(),
									warn: jest.fn()
								}
							},
							{
								provide: DataSource,
								useValue: {
									transaction: jest.fn(),
									manager: { findOne: jest.fn() }
								}
							}
							// Note: SqsService is not provided
						]
					}).compile();

				const serviceWithoutSqs =
					moduleWithoutSqs.get<AvailabilityService>(
						AvailabilityService
					);
				const loggerWithoutSqs =
					moduleWithoutSqs.get<WinstonLogger>(WinstonLogger);

				expect(loggerWithoutSqs.warn).toHaveBeenCalledWith(
					'SqsService not available in AvailabilityService - some functionality may be limited'
				);

				await moduleWithoutSqs.close();
			});
		});

		describe('Private method edge cases', () => {
			it('should handle catch block in isValidTimeFormat', () => {
				// Test the catch block by providing a malformed ISO string that throws an error
				const result = (service as any).isValidTimeFormat(
					'2023-13-45T25:70:80.000Z'
				);
				expect(result).toBe(false);
			});

			it('should handle timezone offset extraction failure', () => {
				// Test with invalid timezone - this will trigger the catch block
				const offset = (service as any).getTimezoneOffset(
					'Invalid/Timezone'
				);
				expect(offset).toBe(0);
				expect(logger.error).toHaveBeenCalledWith(
					expect.stringContaining(
						'Error getting timezone offset for Invalid/Timezone:'
					),
					expect.any(Object)
				);
			});

			it('should handle regex match failure in timezone offset', () => {
				// Mock formatInTimeZone to return a string without proper offset format
				const originalFormatInTimeZone =
					jest.requireActual('date-fns-tz').formatInTimeZone;
				jest.spyOn(
					require('date-fns-tz'),
					'formatInTimeZone'
				).mockReturnValue('2023-01-01T10:00:00.000');

				const offset = (service as any).getTimezoneOffset('UTC');
				expect(offset).toBe(0);
				expect(logger.warn).toHaveBeenCalledWith(
					expect.stringContaining(
						'Failed to extract timezone offset from:'
					)
				);

				// Restore the original function
				require('date-fns-tz').formatInTimeZone.mockRestore();
			});
		});

		describe('Exception processing edge cases', () => {
			it('should process partial-day OUT_OF_OFFICE exception', async () => {
				const startDate = new Date('2023-01-01');
				const endDate = new Date('2023-01-01');
				const partialDayException = {
					...mockException,
					type: ExceptionType.OUT_OF_OFFICE,
					isFullDay: false,
					times: [
						{
							startTime: '10:00:00',
							endTime: '12:00:00'
						}
					]
				};

				dataSource.transaction.mockImplementation(
					async (callback: any) => {
						return await callback(mockEntityManager);
					}
				);

				mockEntityManager.delete.mockResolvedValue({
					affected: 0,
					raw: {}
				});
				mockEntityManager.findOne.mockResolvedValue(mockClinicUser);
				mockEntityManager.find.mockResolvedValue([partialDayException]);
				mockEntityManager.save.mockResolvedValue(mockAvailabilitySlot);
				mockEntityManager.createQueryBuilder.mockReturnValue({
					leftJoinAndSelect: jest.fn().mockReturnThis(),
					where: jest.fn().mockReturnThis(),
					andWhere: jest.fn().mockReturnThis(),
					getMany: jest.fn().mockResolvedValue([])
				} as any);

				await service.generateSlotsForUser(
					'user-123',
					startDate,
					endDate
				);

				expect(logger.log).toHaveBeenCalledWith(
					'Applying partial-day OUT_OF_OFFICE exception',
					expect.objectContaining({
						exceptionId: 'exception-123'
					})
				);
			});

			it('should handle working hours processing errors', async () => {
				const startDate = new Date('2023-01-02'); // Monday
				const endDate = new Date('2023-01-02');
				const userWithBadWorkingHours = {
					...mockClinicUser,
					workingHours: {
						workingHours: {
							monday: [
								{
									isWorkingDay: true,
									startTime: 'invalid-time',
									endTime: '17:00:00'
								}
							]
						}
					}
				} as ClinicUser;

				dataSource.transaction.mockImplementation(
					async (callback: any) => {
						return await callback(mockEntityManager);
					}
				);

				mockEntityManager.delete.mockResolvedValue({
					affected: 0,
					raw: {}
				});
				mockEntityManager.findOne.mockResolvedValue(
					userWithBadWorkingHours
				);
				mockEntityManager.find.mockResolvedValue([]);
				mockEntityManager.createQueryBuilder.mockReturnValue({
					leftJoinAndSelect: jest.fn().mockReturnThis(),
					where: jest.fn().mockReturnThis(),
					andWhere: jest.fn().mockReturnThis(),
					getMany: jest.fn().mockResolvedValue([])
				} as any);

				// Mock localToUtc to throw an error
				jest.spyOn(service as any, 'localToUtc').mockImplementation(
					() => {
						throw new Error('Invalid time conversion');
					}
				);

				await service.generateSlotsForUser(
					'user-123',
					startDate,
					endDate
				);

				expect(logger.error).toHaveBeenCalledWith(
					'Error processing working hours',
					expect.objectContaining({
						clinicUserId: 'user-123',
						error: 'Invalid time conversion'
					})
				);

				// Restore the original method
				jest.restoreAllMocks();
			});
		});

		describe('Complex availability scenarios', () => {
			it('should handle exceptions with date range queries', async () => {
				const startDate = new Date('2023-01-01');
				const endDate = new Date('2023-01-01');
				const dateRangeException = {
					...mockException,
					type: ExceptionType.OUT_OF_OFFICE,
					startDate: new Date('2023-01-01'),
					endDate: new Date('2023-01-03'),
					isFullDay: true
				};

				dataSource.transaction.mockImplementation(
					async (callback: any) => {
						return await callback(mockEntityManager);
					}
				);

				mockEntityManager.delete.mockResolvedValue({
					affected: 0,
					raw: {}
				});
				mockEntityManager.findOne.mockResolvedValue(mockClinicUser);
				mockEntityManager.find.mockResolvedValue([dateRangeException]);
				mockEntityManager.createQueryBuilder.mockReturnValue({
					leftJoinAndSelect: jest.fn().mockReturnThis(),
					where: jest.fn().mockReturnThis(),
					andWhere: jest.fn().mockReturnThis(),
					getMany: jest.fn().mockResolvedValue([])
				} as any);

				await service.generateSlotsForUser(
					'user-123',
					startDate,
					endDate
				);

				expect(mockEntityManager.find).toHaveBeenCalledWith(
					AvailabilityExceptionEntity,
					expect.objectContaining({
						where: expect.arrayContaining([
							expect.objectContaining({
								clinicUserId: 'user-123',
								startDate: expect.any(Object),
								endDate: IsNull()
							}),
							expect.objectContaining({
								clinicUserId: 'user-123',
								startDate: expect.any(Object),
								endDate: expect.any(Object)
							})
						])
					})
				);
			});

			it('should handle multiple working hour blocks in a day', async () => {
				const startDate = new Date('2023-01-02'); // Monday
				const endDate = new Date('2023-01-02');
				const userWithMultipleBlocks = {
					...mockClinicUser,
					workingHours: {
						workingHours: {
							monday: [
								{
									isWorkingDay: true,
									startTime: '09:00:00',
									endTime: '12:00:00'
								},
								{
									isWorkingDay: true,
									startTime: '14:00:00',
									endTime: '17:00:00'
								}
							]
						}
					}
				} as ClinicUser;

				dataSource.transaction.mockImplementation(
					async (callback: any) => {
						return await callback(mockEntityManager);
					}
				);

				mockEntityManager.delete.mockResolvedValue({
					affected: 0,
					raw: {}
				});
				mockEntityManager.findOne.mockResolvedValue(
					userWithMultipleBlocks
				);
				mockEntityManager.find.mockResolvedValue([]);
				mockEntityManager.save.mockResolvedValue(mockAvailabilitySlot);
				mockEntityManager.createQueryBuilder.mockReturnValue({
					leftJoinAndSelect: jest.fn().mockReturnThis(),
					where: jest.fn().mockReturnThis(),
					andWhere: jest.fn().mockReturnThis(),
					getMany: jest.fn().mockResolvedValue([])
				} as any);

				await service.generateSlotsForUser(
					'user-123',
					startDate,
					endDate
				);

				// Should save multiple slots for multiple working hour blocks
				expect(mockEntityManager.save).toHaveBeenCalledTimes(2);
			});
		});

		describe('Edge cases in findAvailableUsers', () => {
			it('should handle empty clinic user list', async () => {
				const mockClinic = {
					id: 'clinic-123',
					timezone: 'Asia/Kolkata'
				} as ClinicEntity;
				const mockQueryBuilder = {
					createQueryBuilder: jest.fn().mockReturnThis(),
					where: jest.fn().mockReturnThis(),
					innerJoin: jest.fn().mockReturnThis(),
					andWhere: jest.fn().mockReturnThis(),
					getMany: jest.fn().mockResolvedValue([]) // No users found
				};

				dataSource.manager.findOne = jest
					.fn()
					.mockResolvedValue(mockClinic);
				clinicUserRepository.createQueryBuilder.mockReturnValue(
					mockQueryBuilder as any
				);

				const result = await service.findAvailableUsers(
					'clinic-123',
					'2023-01-01',
					'09:00:00',
					'10:00:00'
				);

				expect(result).toEqual([]);
				expect(logger.log).toHaveBeenCalledWith(
					'No clinic users found matching filters',
					expect.objectContaining({
						clinicId: 'clinic-123'
					})
				);
			});

			it('should handle availability slots with no matching users', async () => {
				const mockClinic = {
					id: 'clinic-123',
					timezone: 'Asia/Kolkata'
				} as ClinicEntity;
				const mockQueryBuilder = {
					createQueryBuilder: jest.fn().mockReturnThis(),
					where: jest.fn().mockReturnThis(),
					innerJoin: jest.fn().mockReturnThis(),
					andWhere: jest.fn().mockReturnThis(),
					getMany: jest.fn().mockResolvedValue([mockClinicUser])
				};

				dataSource.manager.findOne = jest
					.fn()
					.mockResolvedValue(mockClinic);
				clinicUserRepository.createQueryBuilder.mockReturnValue(
					mockQueryBuilder as any
				);
				availabilityRepository.createQueryBuilder.mockReturnValue({
					where: jest.fn().mockReturnThis(),
					andWhere: jest.fn().mockReturnThis(),
					getMany: jest.fn().mockResolvedValue([]) // No available slots
				} as any);

				const result = await service.findAvailableUsers(
					'clinic-123',
					'2023-01-01',
					'09:00:00',
					'10:00:00'
				);

				expect(result).toEqual([]);
			});
		});

		describe('findAvailableSlotsWithDuration', () => {
			it('should be defined', () => {
				expect(service.findAvailableSlotsWithDuration).toBeDefined();
			});

			it('should return available slots with specified duration', async () => {
				clinicUserRepository.findOne.mockResolvedValue(mockClinicUser);
				availabilityRepository.find.mockResolvedValue([
					{
						...mockAvailabilitySlot,
						startTime: new Date('2023-01-01T09:00:00.000Z'),
						endTime: new Date('2023-01-01T12:00:00.000Z') // 3 hour slot
					}
				]);

				const result = await service.findAvailableSlotsWithDuration(
					'user-123',
					'2023-01-01',
					60 // 60 minutes
				);

				expect(result).toBeDefined();
				expect(Array.isArray(result)).toBe(true);
			});

			it('should return empty array for invalid date format', async () => {
				const result = await service.findAvailableSlotsWithDuration(
					'user-123',
					'invalid-date',
					60
				);

				expect(result).toEqual([]);
				expect(logger.error).toHaveBeenCalledWith(
					'Invalid date format in findAvailableSlotsWithDuration',
					{ date: 'invalid-date' }
				);
			});

			it('should return empty array when clinic user not found', async () => {
				clinicUserRepository.findOne.mockResolvedValue(null);

				const result = await service.findAvailableSlotsWithDuration(
					'user-123',
					'2023-01-01',
					60
				);

				expect(result).toEqual([]);
				expect(logger.error).toHaveBeenCalledWith(
					'Clinic user or clinic not found for duration check',
					{ clinicUserId: 'user-123' }
				);
			});
		});

		describe('getNextAvailableSlot', () => {
			it('should be defined', () => {
				expect(service.getNextAvailableSlot).toBeDefined();
			});

			it('should return next available slot', async () => {
				clinicUserRepository.findOne.mockResolvedValue(mockClinicUser);
				availabilityRepository.findOne.mockResolvedValue(
					mockAvailabilitySlot
				);

				const result = await service.getNextAvailableSlot(
					'user-123',
					'2023-01-01',
					'09:00:00'
				);

				expect(result).toBeDefined();
			});

			it('should return null for invalid date format', async () => {
				const result = await service.getNextAvailableSlot(
					'user-123',
					'invalid-date',
					'09:00:00'
				);

				expect(result).toBeNull();
				expect(logger.error).toHaveBeenCalledWith(
					'Invalid date or time format in getNextAvailableSlot',
					{ fromDate: 'invalid-date', fromTime: '09:00:00' }
				);
			});

			it('should return null when clinic user not found', async () => {
				clinicUserRepository.findOne.mockResolvedValue(null);

				const result = await service.getNextAvailableSlot(
					'user-123',
					'2023-01-01',
					'09:00:00'
				);

				expect(result).toBeNull();
				expect(logger.error).toHaveBeenCalledWith(
					'Clinic user or clinic not found for next slot check',
					{ clinicUserId: 'user-123' }
				);
			});
		});

		describe('getUnavailableDays', () => {
			it('should be defined', () => {
				expect(service.getUnavailableDays).toBeDefined();
			});

			it('should return unavailable days', async () => {
				clinicUserRepository.findOne.mockResolvedValue(mockClinicUser);
				availabilityRepository.createQueryBuilder.mockReturnValue({
					select: jest.fn().mockReturnThis(),
					where: jest.fn().mockReturnThis(),
					andWhere: jest.fn().mockReturnThis(),
					groupBy: jest.fn().mockReturnThis(),
					having: jest.fn().mockReturnThis(),
					getRawMany: jest.fn().mockResolvedValue([])
				} as any);

				const result = await service.getUnavailableDays(
					'user-123',
					'2023-01-01',
					'2023-01-31'
				);

				expect(result).toBeDefined();
				expect(Array.isArray(result)).toBe(true);
			});

			it('should return empty array for invalid date format', async () => {
				const result = await service.getUnavailableDays(
					'user-123',
					'invalid-date',
					'2023-01-31'
				);

				expect(result).toEqual([]);
				expect(logger.error).toHaveBeenCalledWith(
					'Invalid date format in getUnavailableDays',
					expect.objectContaining({
						startDate: 'invalid-date'
					})
				);
			});
		});

		describe('getAllAvailableSlots', () => {
			it('should be defined', () => {
				expect(service.getAllAvailableSlots).toBeDefined();
			});

			it('should return all available slots', async () => {
				clinicUserRepository.findOne.mockResolvedValue(mockClinicUser);
				availabilityRepository.find.mockResolvedValue([
					mockAvailabilitySlot
				]);

				const result = await service.getAllAvailableSlots(
					'user-123',
					'2023-01-01',
					'2023-01-31'
				);

				expect(result).toBeDefined();
				expect(Array.isArray(result)).toBe(true);
			});

			it('should return empty array for invalid date format', async () => {
				const result = await service.getAllAvailableSlots(
					'user-123',
					'invalid-date',
					'2023-01-31'
				);

				expect(result).toEqual([]);
				expect(logger.error).toHaveBeenCalledWith(
					'Invalid date format in getAllAvailableSlots',
					expect.objectContaining({
						startDate: 'invalid-date'
					})
				);
			});
		});

		describe('defragmentSlots', () => {
			it('should be defined', () => {
				expect(service.defragmentSlots).toBeDefined();
			});

			it('should defragment slots successfully', async () => {
				const multipleSlots = [
					{
						...mockAvailabilitySlot,
						id: 'slot-1',
						startTime: new Date('2023-01-01T09:00:00.000Z'),
						endTime: new Date('2023-01-01T12:00:00.000Z')
					},
					{
						...mockAvailabilitySlot,
						id: 'slot-2',
						startTime: new Date('2023-01-01T12:00:00.000Z'),
						endTime: new Date('2023-01-01T17:00:00.000Z')
					}
				];

				dataSource.transaction.mockImplementation(
					async (callback: any) => {
						return await callback(mockEntityManager);
					}
				);
				mockEntityManager.find.mockResolvedValue(multipleSlots);
				mockEntityManager.save.mockResolvedValue(mockAvailabilitySlot);

				await service.defragmentSlots('user-123', '2023-01-01');

				expect(mockEntityManager.find).toHaveBeenCalledWith(
					ClinicAvailabilitySlot,
					{
						where: {
							clinicUserId: 'user-123',
							date: '2023-01-01',
							isAvailable: true
						},
						order: {
							startTime: 'ASC'
						}
					}
				);
			});

			it('should handle single slot without defragmentation', async () => {
				dataSource.transaction.mockImplementation(
					async (callback: any) => {
						return await callback(mockEntityManager);
					}
				);
				mockEntityManager.find.mockResolvedValue([
					mockAvailabilitySlot
				]);

				await service.defragmentSlots('user-123', '2023-01-01');

				expect(logger.log).toHaveBeenCalledWith(
					'Nothing to defragment, 1 or fewer slots found',
					expect.objectContaining({
						clinicUserId: 'user-123',
						date: '2023-01-01'
					})
				);
			});
		});

		describe('validateSlotConsistency', () => {
			it('should be defined', () => {
				expect(service.validateSlotConsistency).toBeDefined();
			});

			it('should validate slot consistency successfully', async () => {
				appointmentDoctorsRepository.createQueryBuilder.mockReturnValue(
					{
						leftJoinAndSelect: jest.fn().mockReturnThis(),
						where: jest.fn().mockReturnThis(),
						andWhere: jest.fn().mockReturnThis(),
						getMany: jest.fn().mockResolvedValue([]) // No appointments
					} as any
				);

				availabilityRepository.find.mockResolvedValue([]); // No slots to avoid complex validation
				
				clinicUserRepository.findOne.mockResolvedValue({
					...mockClinicUser,
					workingHours: null // No working hours to simplify validation
				});

				const result = await service.validateSlotConsistency(
					'user-123',
					'2023-01-01'
				);

				expect(result).toBe(true);
			});

			it('should return false for invalid date format', async () => {
				const result = await service.validateSlotConsistency(
					'user-123',
					'invalid-date'
				);

				expect(result).toBe(false);
				expect(logger.error).toHaveBeenCalledWith(
					'Invalid date format in validateSlotConsistency',
					{ date: 'invalid-date' }
				);
			});
		});

		describe('handleAppointmentChange', () => {
			it('should be defined', () => {
				expect(service.handleAppointmentChange).toBeDefined();
			});

			it('should handle appointment creation', async () => {
				const mockAppointment = {
					id: 'appointment-123',
					date: new Date('2023-01-01'),
					startTime: new Date('2023-01-01T10:00:00.000Z'),
					endTime: new Date('2023-01-01T11:00:00.000Z')
				} as AppointmentEntity;

				appointmentDoctorsRepository.find.mockResolvedValue([
					mockAppointmentDoctor
				]);

				await service.handleAppointmentChange(
					mockAppointment,
					'create',
					undefined
				);

				expect(logger.log).toHaveBeenCalledWith(
					'Handling appointment change',
					expect.objectContaining({
						appointmentId: 'appointment-123',
						action: 'create'
					})
				);
			});

			it('should handle appointment deletion', async () => {
				const mockAppointment = {
					id: 'appointment-123',
					date: new Date('2023-01-01'),
					startTime: new Date('2023-01-01T10:00:00.000Z'),
					endTime: new Date('2023-01-01T11:00:00.000Z'),
					appointmentDoctors: [mockAppointmentDoctor]
				} as AppointmentEntity;

				await service.handleAppointmentChange(
					mockAppointment,
					'delete',
					undefined
				);

				expect(logger.log).toHaveBeenCalledWith(
					'Handling appointment change',
					expect.objectContaining({
						appointmentId: 'appointment-123',
						action: 'delete'
					})
				);
			});
		});

		describe('handleExceptionChange', () => {
			it('should be defined', () => {
				expect(service.handleExceptionChange).toBeDefined();
			});

			it('should handle exception creation', async () => {
				const mockExceptionWithEndDate = {
					...mockException,
					endDate: new Date('2023-01-02')
				};

				await service.handleExceptionChange(
					mockExceptionWithEndDate,
					'create',
					undefined
				);

				expect(logger.log).toHaveBeenCalledWith(
					'Handling exception change',
					expect.objectContaining({
						exceptionId: 'exception-123',
						action: 'create'
					})
				);
			});

			it('should handle single day exception', async () => {
				await service.handleExceptionChange(
					mockException,
					'create',
					undefined
				);

				expect(logger.log).toHaveBeenCalledWith(
					'Handling exception change',
					expect.objectContaining({
						exceptionId: 'exception-123'
					})
				);
			});
		});

		describe('handleWorkingHoursChange', () => {
			it('should be defined', () => {
				expect(service.handleWorkingHoursChange).toBeDefined();
			});

			it('should handle working hours change', async () => {
				await service.handleWorkingHoursChange('user-123');

				expect(logger.log).toHaveBeenCalledWith(
					'Handling working hours change',
					{ clinicUserId: 'user-123' }
				);
			});
		});

		describe('getAvailableDatesForDoctor', () => {
			it('should be defined', () => {
				expect(service.getAvailableDatesForDoctor).toBeDefined();
			});

			it('should return available dates', async () => {
				clinicUserRepository.findOne.mockResolvedValue(mockClinicUser);
				availabilityRepository.createQueryBuilder.mockReturnValue({
					select: jest.fn().mockReturnThis(),
					where: jest.fn().mockReturnThis(),
					andWhere: jest.fn().mockReturnThis(),
					groupBy: jest.fn().mockReturnThis(),
					having: jest.fn().mockReturnThis(),
					orderBy: jest.fn().mockReturnThis(),
					getRawMany: jest
						.fn()
						.mockResolvedValue([{ date: '2023-01-01' }])
				} as any);

				const result = await service.getAvailableDatesForDoctor(
					'user-123',
					'2023-01-01',
					'2023-01-31'
				);

				expect(result).toEqual(['2023-01-01']);
			});

			it('should return empty array for invalid date format', async () => {
				const result = await service.getAvailableDatesForDoctor(
					'user-123',
					'invalid-date',
					'2023-01-31'
				);

				expect(result).toEqual([]);
				expect(logger.error).toHaveBeenCalledWith(
					'Invalid date format in getAvailableDatesForDoctor',
					expect.objectContaining({
						startDate: 'invalid-date'
					})
				);
			});
		});

		describe('getAvailableTimeSlotsForDate', () => {
			it('should be defined', () => {
				expect(service.getAvailableTimeSlotsForDate).toBeDefined();
			});

			it('should return available time slots for date', async () => {
				clinicUserRepository.findOne.mockResolvedValue(mockClinicUser);
				availabilityRepository.find.mockResolvedValue([
					mockAvailabilitySlot
				]);

				const result = await service.getAvailableTimeSlotsForDate(
					'user-123',
					'2023-01-01'
				);

				expect(result).toBeDefined();
				expect(Array.isArray(result)).toBe(true);
			});

			it('should return empty array for invalid date format', async () => {
				const result = await service.getAvailableTimeSlotsForDate(
					'user-123',
					'invalid-date'
				);

				expect(result).toEqual([]);
				expect(logger.error).toHaveBeenCalledWith(
					'Invalid date format in getAvailableTimeSlotsForDate',
					{ date: 'invalid-date' }
				);
			});
		});

		describe('getAvailableTimeSlotsForMultipleDoctors', () => {
			it('should be defined', () => {
				expect(
					service.getAvailableTimeSlotsForMultipleDoctors
				).toBeDefined();
			});

			it('should return time slots for multiple doctors', async () => {
				clinicUserRepository.find.mockResolvedValue([
					mockClinicUser,
					{ ...mockClinicUser, id: 'user-124' }
				]);
				availabilityRepository.find.mockResolvedValue([
					mockAvailabilitySlot
				]);

				const result =
					await service.getAvailableTimeSlotsForMultipleDoctors(
						['user-123', 'user-124'],
						'2023-01-01'
					);

				expect(result).toBeInstanceOf(Map);
			});

			it('should return empty map for invalid date format', async () => {
				const result =
					await service.getAvailableTimeSlotsForMultipleDoctors(
						['user-123'],
						'invalid-date'
					);

				expect(result).toBeInstanceOf(Map);
				expect(result.size).toBe(0);
				expect(logger.error).toHaveBeenCalledWith(
					'Invalid date format in getAvailableTimeSlotsForMultipleDoctors',
					{ date: 'invalid-date' }
				);
			});
		});

		describe('getFilteredAvailableDatesForDoctors', () => {
			it('should be defined', () => {
				expect(
					service.getFilteredAvailableDatesForDoctors
				).toBeDefined();
			});

			it('should return filtered available dates', async () => {
				availabilityRepository.find.mockResolvedValue([]); // No slots available

				const result =
					await service.getFilteredAvailableDatesForDoctors(
						['user-123'],
						'2023-01-01',
						'2023-01-31',
						{
							clinicTimezone: 'UTC',
							workingHours: {},
							minBookingLeadMinutes: 0,
							slotDurationMinutes: 30
						}
					);

				expect(result).toEqual([]); // Expect empty array when no slots
			});

			it('should return empty array for invalid date format', async () => {
				const result =
					await service.getFilteredAvailableDatesForDoctors(
						['user-123'],
						'invalid-date',
						'2023-01-31',
						{
							clinicTimezone: 'Asia/Kolkata',
							slotDurationMinutes: 30
						}
					);

				expect(result).toEqual([]);
				expect(logger.error).toHaveBeenCalledWith(
					'Invalid date format in getFilteredAvailableDatesForDoctors',
					expect.objectContaining({
						startDate: 'invalid-date'
					})
				);
			});

			it('should return empty array for empty doctor list', async () => {
				const result =
					await service.getFilteredAvailableDatesForDoctors(
						[],
						'2023-01-01',
						'2023-01-31',
						{
							clinicTimezone: 'Asia/Kolkata',
							slotDurationMinutes: 30
						}
					);

				expect(result).toEqual([]);
				expect(logger.warn).toHaveBeenCalledWith(
					'No doctorIds provided to getFilteredAvailableDatesForDoctors'
				);
			});
		});
		describe('Additional edge cases and error scenarios', () => {
			it('should handle timezone offset extraction failure', () => {
				// Test the private getTimezoneOffset method with invalid timezone
				const offset = (service as any).getTimezoneOffset(
					'Invalid/Timezone'
				);
				expect(offset).toBe(0);
			});

			it('should handle timezone offset with different date', () => {
				const testDate = new Date('2023-06-15'); // Summer date for DST testing
				const offset = (service as any).getTimezoneOffset(
					'America/New_York',
					testDate
				);
				expect(typeof offset).toBe('number');
			});

			it('should handle localToUtc conversion errors', () => {
				// Test error handling in localToUtc
				const result = (service as any).localToUtc(
					'invalid-date-string',
					'Asia/Kolkata'
				);
				expect(result).toBeInstanceOf(Date);
			});

			it('should handle removeAppointmentTime with complex overlaps', () => {
				const slots = [
					{
						start: new Date('2023-01-01T09:00:00.000Z'),
						end: new Date('2023-01-01T12:00:00.000Z')
					},
					{
						start: new Date('2023-01-01T14:00:00.000Z'),
						end: new Date('2023-01-01T17:00:00.000Z')
					}
				];
				const appointmentStart = new Date('2023-01-01T10:30:00.000Z');
				const appointmentEnd = new Date('2023-01-01T11:30:00.000Z');

				const result = (service as any).removeAppointmentTime(
					slots,
					appointmentStart,
					appointmentEnd
				);

				expect(result).toHaveLength(3); // Should split first slot and keep second slot
			});

			it('should handle isValidTimeFormat with milliseconds', () => {
				const isValid1 = (service as any).isValidTimeFormat(
					'10:30:45.123'
				);
				const isValid2 = (service as any).isValidTimeFormat(
					'23:59:59.999'
				);
				expect(isValid1).toBe(true);
				expect(isValid2).toBe(true);
			});

			it('should handle isValidDateFormat ', () => {
				const isValid1 = (service as any).isValidDateFormat(
					'2024-02-29'
				);
				const isValid2 = (service as any).isValidDateFormat(
					'invalid-date'
				); // Completely invalid format
				expect(isValid1).toBe(true);
				expect(isValid2).toBe(false);
			});
		});
	});
});
