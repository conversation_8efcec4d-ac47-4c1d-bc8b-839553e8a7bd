import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppointmentEntity } from './entities/appointment.entity';
import { AppointmentsController } from './appointments.controller';
import { AppointmentsService } from './appointments.service';
import { AppointmentDoctorsEntity } from './entities/appointment-doctor.entity';
import { AppointmentDetailsEntity } from './entities/appointment-details.entity';
import { SESModule } from '../utils/aws/ses/ses.module';
import { Task } from '../tasks/entities/tasks.entity';
import { TasksService } from '../tasks/tasks.service';
import { RoleModule } from '../roles/role.module';
import { WhatsappModule } from '../utils/whatsapp-integration/whatsapp.module';
import { EmrService } from '../emr/emr.service';
import { Emr } from '../emr/entities/emr.entity';
import { S3Service } from '../utils/aws/s3/s3.service';
import { PatientRemindersService } from '../patient-reminders/patient-reminder.service';
import { PatientReminder } from '../patient-reminders/entities/patient-reminder.entity';
import { PatientReminderHistory } from '../patient-reminders/entities/patient-reminder-history.entity';
import { SqsService } from '../utils/aws/sqs/sqs.service';
import { GlobalReminderModule } from '../patient-global-reminders/global-reminders.module';
import { LabReport } from '../clinic-lab-report/entities/lab-report.entity';
import { ClinicLabReportModule } from '../clinic-lab-report/clinic-lab-report.module';
import { AvailabilityModule } from '../availability/availability.module';
import { CartsModule } from '../carts/carts.module';
import { LongTermMedicationsModule } from '../long-term-medications/long-term-medications.module';
import { SocketModule } from '../socket/socket.module';
import { GoogleCalendarModule } from '../google-calendar/google-calendar.module';
import { User } from '../users/entities/user.entity';
import { ParseArrayPipe } from '../utils/pipes/parse-array.pipe';
import { RedisModule } from '../utils/redis/redis.module';

@Module({
	imports: [
		TypeOrmModule.forFeature([
			AppointmentEntity,
			AppointmentDoctorsEntity,
			AppointmentDetailsEntity,
			Task,
			Emr,
			PatientReminder,
			PatientReminderHistory,
			LabReport,
			User
		]),
		SESModule,
		RoleModule,
		WhatsappModule,
		GlobalReminderModule,
		ClinicLabReportModule,
		LongTermMedicationsModule,
		GoogleCalendarModule,
		RedisModule,
		forwardRef(() => AvailabilityModule),
		forwardRef(() => CartsModule),
		forwardRef(() => SocketModule)
	],
	controllers: [AppointmentsController],
	providers: [
		AppointmentsService,
		TasksService,
		EmrService,
		S3Service,
		PatientRemindersService,
		SqsService,
		ParseArrayPipe
	],
	exports: [AppointmentsService]
})
export class AppointmentsModule {}
