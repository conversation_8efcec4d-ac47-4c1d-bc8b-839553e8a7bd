import { Injectable, Logger } from '@nestjs/common';
import { Message } from '@aws-sdk/client-sqs';
import { QueueHandler } from '../interfaces/queue-handler.interface';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { GoogleCalendarService } from '../../../../google-calendar/google-calendar.service';
import { User } from '../../../../users/entities/user.entity';

export interface GoogleCalendarSyncMessage {
	type: 'WEBHOOK_NOTIFICATION' | 'INCREMENTAL_SYNC' | 'SYNC_REFRESH';
	userId: string;
	appointmentId?: string;
	googleEventId?: string;
	retryCount?: number;
	webhookData?: any;
	timestamp: string;
}

@Injectable()
export class ProcessGoogleCalendarSyncHandler implements QueueHandler {
	private readonly logger = new Logger(ProcessGoogleCalendarSyncHandler.name);

	constructor(
		@InjectRepository(User)
		private readonly userRepository: Repository<User>,
		private readonly googleCalendarService: GoogleCalendarService
	) {}

	async handle(message: Message): Promise<void> {
		const body = JSON.parse(message.Body || '{}');
		const data: GoogleCalendarSyncMessage = body?.data || body;
		this.logger.log(
			`🚀 [SQS_HANDLER] Processing Google Calendar sync: ${data.type} for user ${data.userId}`,
			{ appointmentId: data.appointmentId, type: data.type, timestamp: data.timestamp }
		);

		try {
			switch (data.type) {
				case 'WEBHOOK_NOTIFICATION':
					await this.handleWebhookNotification(data);
					break;
				case 'INCREMENTAL_SYNC':
				case 'SYNC_REFRESH':
					await this.googleCalendarService.performIncrementalSync(data.userId);
					break;
				default:
					throw new Error(`Unknown Google Calendar sync type: ${data.type}`);
			}

			this.logger.log(
				`Successfully processed Google Calendar sync: ${data.type}`,
				{ userId: data.userId, appointmentId: data.appointmentId }
			);
		} catch (error: any) {
			this.logger.error(
				`Failed to process Google Calendar sync: ${data.type}`,
				{
					error: error.message,
					stack: error.stack,
					data,
					retryCount: data.retryCount || 0
				}
			);
			throw error; // Re-throw to trigger SQS retry mechanism
		}
	}

	private async handleWebhookNotification(message: GoogleCalendarSyncMessage): Promise<void> {
		this.logger.log(`🔔 [SQS_WEBHOOK] Processing Google Calendar webhook notification for user ${message.userId}`);
		this.logger.debug(`🔍 [SQS_WEBHOOK] Webhook data:`, message.webhookData);
		
		try {
			// Verify user exists and has Google Calendar sync enabled with retries
			let user: User | null = null;
			let retryCount = 0;
			const maxRetries = 3;
			
			while (retryCount < maxRetries && !user) {
				user = await this.userRepository.findOne({
					where: { id: message.userId }
				});
				
				if (!user) {
					retryCount++;
					this.logger.warn(`⚠️ [SQS_WEBHOOK] User ${message.userId} not found (attempt ${retryCount}/${maxRetries}), waiting before retry...`);
					if (retryCount < maxRetries) {
						// Wait progressively longer between retries
						await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
					}
				}
			}

			if (!user) {
				this.logger.error(`❌ [SQS_WEBHOOK] User ${message.userId} not found after ${maxRetries} attempts`);
				throw new Error(`User ${message.userId} not found`);
			}

			this.logger.log(`👤 [SQS_WEBHOOK] User found: ${user.email}, isGoogleSyncEnabled: ${user.isGoogleSyncEnabled}, calendarId: ${user.googleCalendarId}`);

			if (!user.isGoogleSyncEnabled || !user.googleCalendarId) {
				this.logger.warn(`⚠️ [SQS_WEBHOOK] Google Calendar sync is not enabled for user ${message.userId}. isGoogleSyncEnabled: ${user.isGoogleSyncEnabled}, calendarId: ${user.googleCalendarId}`);
				return;
			}

			// Note: Removed timestamp check that was causing legitimate webhooks to be ignored.
			// The webhook timestamp represents when Nidana received the webhook, not when the
			// Google Calendar event was actually modified, which could cause valid notifications
			// to be skipped if a scheduled sync ran between the event modification and webhook delivery.
			this.logger.log(`🔄 [SQS_WEBHOOK] Processing webhook notification for user ${message.userId} (last sync: ${user.lastGoogleSyncAt})`);


			// Trigger incremental sync to fetch changes from Google Calendar
			this.logger.log(`🔄 [SQS_WEBHOOK] Triggering incremental sync for user ${message.userId}`);
			await this.googleCalendarService.performIncrementalSync(message.userId);
			
			this.logger.log(`✅ [SQS_WEBHOOK] Google Calendar webhook notification processed successfully for user ${message.userId}`);
		} catch (error: any) {
			this.logger.error(`💥 [SQS_WEBHOOK] Failed to process webhook notification: ${error.message}`, error.stack);
			throw error;
		}
	}
} 