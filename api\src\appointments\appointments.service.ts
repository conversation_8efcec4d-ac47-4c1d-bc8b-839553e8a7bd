import {
	BadRequestException,
	forwardRef,
	Inject,
	Injectable,
	InternalServerErrorException,
	NotFoundException
} from '@nestjs/common';
import { CreateAppointmentDto } from './dto/create/create-appointment.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { AppointmentEntity } from './entities/appointment.entity';
import {
	DeepPartial,
	Repository,
	UpdateResult,
	IsNull,
	Brackets,
	In,
	Not,
	FindOptionsWhere,
	FindManyOptions
} from 'typeorm';
import { AppointmentDoctorsEntity } from './entities/appointment-doctor.entity';
import { EnumAppointmentStatus } from './enums/enum-appointment-status';
import { AppointmentDetailsEntity } from './entities/appointment-details.entity';
import { UpdateAppointmentDetailsDto } from './dto/details/update-appointment-details.dto';
import { UpdateAppointmentsDto } from './dto/create/update-appointment.dto';
import { UpdateAppointmentFeildsDto } from './dto/create/update-appointmentField.dto';
import { SESMailService } from '../utils/aws/ses/send-mail-service';
import moment = require('moment-timezone');
import { TasksService } from '../tasks/tasks.service';
import { CreateTaskDto } from '../tasks/dto/create-task.dto';
import { Cron } from '@nestjs/schedule';
import { DEV_SES_EMAIL } from '../utils/constants';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { isProduction, isProductionOrUat } from '../utils/common/get-login-url';
import { WhatsappService } from '../utils/whatsapp-integration/whatsapp.service';
import {
	appointmentCancelMailGenerator,
	appointmentConfirmartionMailGenerator,
	appointmentUpdateMailGenerator
} from '../utils/mail-generator/mail-template-generator';
import {
	getAppointmentCancellationTemplateData,
	getAppointmentCreatationTemplateData,
	getAppointmentUpdateTemplateData,
	getAppointmentCreatationClinicLinkTemplateData,
	getAppointmentUpdateClinicLinkTemplateData,
	getAppointmentCancellationClinicLinkTemplateData
} from '../utils/communicatoins/whatsapp-template-generator';
import { UpdateAppointmentSendMailFlag } from '../utils/common/numeric-transformer';
import { EmrService } from '../emr/emr.service';
import { generateAppointmentSummary } from '../utils/pdfs/todays-appoitments';
import { generatePDF } from '../utils/generatePdf';
import * as path from 'path';
import * as fs from 'fs/promises';
import { ReminderStatus } from '../patient-reminders/enums/reminder.enum';
import { PatientRemindersService } from '../patient-reminders/patient-reminder.service';
import { calculateAge } from '../invoice/invoice.service';
import { SqsService } from '../utils/aws/sqs/sqs.service';
import { GlobalReminderService } from '../patient-global-reminders/global-reminders.service';
import { EnumAppointmentType } from './enums/enum-appointment-type';
import { AvailabilityService } from '../availability/availability.service';
import { selectTemplate } from '../utils/common/template-helper.util';
import { v4 as uuidv4 } from 'uuid';
import { LongTermMedicationsService } from '../long-term-medications/long-term-medications.service';
import { AppointmentGateway } from '../socket/socket.appointment.gateway';
import { GoogleCalendarService } from '../google-calendar/google-calendar.service';
import { GoogleCalendarCacheService } from '../google-calendar/google-calendar-cache.service';
import { User } from '../users/entities/user.entity';
import { ClinicUser } from '../clinics/entities/clinic-user.entity';
import { RedisService } from '../utils/redis/redis.service';

@Injectable()
export class AppointmentsService {
	constructor(
		@InjectRepository(AppointmentEntity)
		private readonly appointmentRepository: Repository<AppointmentEntity>,
		@InjectRepository(AppointmentDoctorsEntity)
		private readonly appointmentDoctorsRepository: Repository<AppointmentDoctorsEntity>,
		@InjectRepository(User)
		private userRepository: Repository<User>,
		@InjectRepository(AppointmentDetailsEntity)
		private readonly appointmentDetailsRepository: Repository<AppointmentDetailsEntity>,
		private readonly mailService: SESMailService,
		private readonly tasksService: TasksService,
		private readonly logger: WinstonLogger,
		private readonly whatsappService: WhatsappService,
		private readonly remindersService: PatientRemindersService,
		@Inject(forwardRef(() => EmrService))
		private readonly emrService: EmrService,
		@Inject(forwardRef(() => SqsService))
		private readonly sqsService: SqsService,
		private readonly globalReminderService: GlobalReminderService,
		@Inject(forwardRef(() => AvailabilityService))
		private readonly availabilityService: AvailabilityService,
		private readonly longTermMedicationsService: LongTermMedicationsService,
		@Inject(forwardRef(() => AppointmentGateway))
		private readonly appointmentGateway: AppointmentGateway,
		private readonly googleCalendarService: GoogleCalendarService,
		private readonly googleCalendarCache: GoogleCalendarCacheService,
		private readonly redisService: RedisService
	) {}

	async createAppointment(
		createAppointmentDto: CreateAppointmentDto,
		brandId: string,
		createdByUserId?: string
	): Promise<AppointmentEntity> {
		createAppointmentDto.status = EnumAppointmentStatus.Scheduled;

		const appointment: AppointmentEntity =
			this.appointmentRepository.create({
				...createAppointmentDto,
				date: new Date(createAppointmentDto.date),
				brandId,
				createdBy: createdByUserId
			} as unknown as DeepPartial<AppointmentEntity>);

		const createdAppointment =
			await this.appointmentRepository.save(appointment);

		if (!createdAppointment) {
			throw new InternalServerErrorException(
				'Failed in creating an appointment!'
			);
		}

		const createAppointmentDetails =
			await this.appointmentDetailsRepository.save({
				appointmentId: createdAppointment.id,
				details: await this.generateDefaultTreatmentDetails(
					createdAppointment.id,
					createAppointmentDto.patientId
				)
			});

		if (!createAppointmentDetails) {
			throw new InternalServerErrorException(
				'Failed in creating appointment details!'
			);
		}

		const doctorPromises = createAppointmentDto.doctorIds.map(
			async doctorId => {
				const createdAppointentDoctorEntry =
					await this.appointmentDoctorsRepository.save({
						appointmentId: createdAppointment.id,
						doctorId: doctorId,
						primary: true
					});

				if (!createdAppointentDoctorEntry) {
					throw new InternalServerErrorException(
						'Failed in creating an appointment..!!!'
					);
				}
			}
		);

		const providerPromises = createAppointmentDto?.providerIds.map(
			async id => {
				const createdAppointentDoctorEntry =
					await this.appointmentDoctorsRepository.save({
						appointmentId: createdAppointment.id,
						doctorId: id,
						primary: false
					});

				if (!createdAppointentDoctorEntry) {
					throw new InternalServerErrorException(
						'Failed in creating an appointment..!!!'
					);
				}
			}
		);

		await Promise.all([...doctorPromises, ...providerPromises]);

		// After ensuring appointment-doctor links are saved, sync with Google Calendar
		await this.syncOnAppointmentCreate(createdAppointment, createdByUserId);

		const appointmentDetails: AppointmentEntity =
			(await this.appointmentRepository.findOne({
				where: { id: appointment.id },
				relations: [
					'appointmentDoctors',
					'appointmentDoctors.clinicUser',
					'appointmentDoctors.clinicUser.user',
					'room',
					'appointmentDetails',
					'patient',
					'patient.patientOwners',
					'patient.patientOwners.ownerBrand',
					'patient.patientOwners.ownerBrand.globalOwner',
					'clinic',
					'clinic.brand'
				]
			})) as AppointmentEntity;

		// Handle midnight-crossing appointments
		const startTime = new Date(
			`${createAppointmentDto.date}T${createAppointmentDto.startTime}`
		);
		const endTime = new Date(
			`${createAppointmentDto.date}T${createAppointmentDto.endTime}`
		);

		if (endTime < startTime) {
			// This is a midnight-crossing appointment
			this.logger.log('Midnight-crossing appointment detected', {
				appointmentId: createdAppointment.id,
				date: createAppointmentDto.date
			});

			// TODO: In the future, we'll need to handle this by affecting availability on two days
			// Current implementation will treat this as same-day appointment
		}
		// Update availability for all involved doctors
		try {
			appointmentDetails.date = moment
				.utc(createAppointmentDto.date, 'DD-MMM-YYYY')
				.toDate();
			await this.availabilityService.handleAppointmentChange(
				appointmentDetails,
				'create'
			);
		} catch (error) {
			this.logger.error(
				'Error updating availability after creating appointment',
				{
					appointmentId: createdAppointment.id,
					error
				}
			);
			// Don't fail the appointment creation if availability update fails
			// The availability system will recover through scheduled validation
		}

		appointmentDetails?.patient?.patientOwners.forEach(
			(patientOwner: any) => {
				// Use ownerBrand and nested globalOwner for patient owner details
				const ownerFirstName =
					patientOwner?.ownerBrand?.firstName || '';
				const ownerLastName = patientOwner?.ownerBrand?.lastName || '';
				const ownerMobileNumber = `${patientOwner?.ownerBrand?.globalOwner?.countryCode || ''}${patientOwner?.ownerBrand?.globalOwner?.phoneNumber || ''}`;

				const { body, subject, toMailAddress } =
					appointmentConfirmartionMailGenerator({
						appointmentdate: moment(
							appointmentDetails?.date
						).format('MMMM Do YYYY'),
						appointmentTime: `${moment(appointmentDetails?.startTime).add(5, 'hours').add(30, 'minute').format('h:mm a')}`,
						brandName: appointmentDetails?.clinic?.brand?.name,
						contactInformation:
							appointmentDetails?.clinic?.phoneNumbers?.[0]
								?.number || 'provided contact no.',
						email: patientOwner?.ownerBrand?.email,
						firstname: ownerFirstName,
						lastName: ownerLastName,
						clinicAddress: `${appointmentDetails?.clinic?.addressLine1 || ''} ${appointmentDetails?.clinic?.addressLine2 || ''}, ${appointmentDetails?.clinic?.city || ''}, ${appointmentDetails?.clinic?.state || ''} ${appointmentDetails?.clinic?.addressPincode || ''}, ${appointmentDetails?.clinic?.country || ''}`
					});
				if (ownerMobileNumber) {
					const templateArgs = {
						appointmentDate: moment(
							appointmentDetails?.date
						).format('MMMM Do YYYY'),
						appointmentTime: `${moment(appointmentDetails?.startTime).add(5, 'hours').add(30, 'minute').format('h:mm a')}`,
						brandName: appointmentDetails?.clinic?.brand?.name,
						contactInformation:
							appointmentDetails?.clinic?.phoneNumbers?.[0]
								?.number,
						clientName: `${ownerFirstName} ${ownerLastName}`,
						mobileNumber: ownerMobileNumber,
						clinicAddress: `${appointmentDetails?.clinic?.addressLine1 || ''} ${appointmentDetails?.clinic?.addressLine2 || ''}, ${appointmentDetails?.clinic?.city || ''}, ${appointmentDetails?.clinic?.state || ''} ${appointmentDetails?.clinic?.addressPincode || ''}, ${appointmentDetails?.clinic?.country || ''}`
					};

					// Use the selectTemplate utility function to choose appropriate template
					const { mobileNumber, templateName, valuesArray } =
						selectTemplate(
							appointmentDetails?.clinic,
							templateArgs,
							getAppointmentCreatationTemplateData,
							getAppointmentCreatationClinicLinkTemplateData
						);

					if (isProductionOrUat()) {
						this.whatsappService
							.sendTemplateMessage({
								templateName,
								valuesArray,
								mobileNumber
							})
							.catch(err => {
								this.logger.error(
									'error in sending whatsapp message',
									{
										err
									}
								);
							});
					}
				}
				if (isProduction() && patientOwner?.ownerBrand?.email) {
					this.mailService.sendMail({
						body,
						subject,
						toMailAddress
					});
				} else if (!isProduction()) {
					this.mailService.sendMail({
						body,
						subject,
						toMailAddress: DEV_SES_EMAIL
					});
				}
			}
		);

		return createdAppointment;
	}

	private async syncOnAppointmentCreate(
		appointment: AppointmentEntity,
		createdByUserId?: string
	) {
		try {
			this.logger.debug('Starting Google Calendar sync for appointment', {
				appointmentId: appointment.id
			});
			// Reload appointment with relations so we have patient, clinic & doctor details for calendar description
			const appointmentWithRelations =
				await this.appointmentRepository.findOne({
					where: { id: appointment.id },
					relations: [
						'patient',
						'clinic',
						'appointmentDoctors',
						'appointmentDoctors.clinicUser',
						'appointmentDoctors.clinicUser.user'
					]
				});

			if (!appointmentWithRelations) {
				this.logger.warn(
					`Appointment ${appointment.id} not found when attempting Google Calendar sync.`
				);
				return;
			}

			let user: User | null = null;

			this.logger.debug(
				'Loaded appointment with relations for Google Calendar sync',
				{
					appointmentId: appointment.id,
					doctorCount:
						appointmentWithRelations.appointmentDoctors?.length || 0
				}
			);
			// 1. Prefer the primary/first doctor attached to the appointment who has Google Calendar connected
			for (const doc of appointmentWithRelations.appointmentDoctors ??
				[]) {
				const docUser = doc?.clinicUser?.user;
				if (
					docUser &&
					docUser.isGoogleSyncEnabled &&
					docUser.googleCalendarRefreshToken &&
					docUser.googleCalendarId
				) {
					user = docUser;
					break;
				}
			}

			// If no suitable Google-connected doctor found, we skip calendar sync
			if (!user) {
				return; // No eligible user
			}

			if (
				user &&
				user.isGoogleSyncEnabled &&
				user.googleCalendarRefreshToken &&
				user.googleCalendarId
			) {
				const googleEventId =
					await this.googleCalendarService.createEvent(
						user.id,
						appointmentWithRelations
					);
				if (googleEventId) {
					await this.appointmentRepository.update(appointment.id, {
						googleEventId
					});
				}
			}
		} catch (error) {
			this.logger.error(`Error syncing appointment ${appointment.id}:`, {
				error: (error as Error).message,
				appointmentId: appointment.id,
				patientId: appointment.patientId
			});
		}
	}

	async getAllAppointments(
		page: number = 1,
		limit: number = 10,
		orderBy: string = 'DESC',
		date = '',
		search: string = '',
		doctors: Array<any> = [],
		status: Array<any> = [],
		onlyPrimary: boolean = false,
		clinicId: string = '',
		includeGoogleEvents: boolean = false,
		viewerUserId?: string
	) {
		const startDate = new Date(date);
		startDate.setHours(0, 0, 0, 0);
		const endDate = new Date(date);
		endDate.setHours(23, 59, 59, 999);

		const query = this.appointmentRepository
			.createQueryBuilder('appointment')
			.leftJoinAndSelect('appointment.patient', 'patient')
			.leftJoinAndSelect('patient.patientOwners', 'patientOwner')
			.leftJoinAndSelect('patientOwner.ownerBrand', 'ownerBrand')
			.leftJoinAndSelect('ownerBrand.globalOwner', 'globalOwner')
			.leftJoinAndSelect(
				'appointment.appointmentDoctors',
				'appointmentDoctors'
			)
			.leftJoinAndSelect('appointmentDoctors.clinicUser', 'clinicUser')
			.leftJoinAndSelect('clinicUser.user', 'doctor')
			.leftJoinAndSelect('appointment.room', 'room')
			.where('appointment.deletedAt IS NULL')
			.andWhere('appointment.clinicId = :clinicId', { clinicId })
			.andWhere('appointment.type != :impromptuType', {
				impromptuType: EnumAppointmentType.Impromptu
			}); // Filter out impromptu appointments
		// .andWhere('appointment.date BETWEEN :startDate AND :endDate', {
		// 	startDate,
		// 	endDate
		// });

		if (date) {
			const startDate = new Date(date);
			startDate.setHours(0, 0, 0, 0);
			const endDate = new Date(date);
			endDate.setHours(23, 59, 59, 999);

			// this.logger.log('startDate, endDate', { startDate, endDate });
			// Add date filter if provided
			query.andWhere('appointment.date BETWEEN :startDate AND :endDate', {
				startDate,
				endDate
			});
		}
		if (onlyPrimary) {
			query.andWhere('appointmentDoctors.primary = :isPrimary', {
				isPrimary: true
			});
		}
		if (search && search.trim() !== '') {
			const tokens = search.split(/\s+/);
			tokens.forEach((token, index) => {
				const paramName = `token_${index}`;
				query.andWhere(
					new Brackets(qb2 => {
						qb2.where(`ownerBrand.firstName ILIKE :${paramName}`, {
							[paramName]: `%${token}%`
						})
							.orWhere(
								`ownerBrand.lastName ILIKE :${paramName}`,
								{
									[paramName]: `%${token}%`
								}
							)
							.orWhere(
								`globalOwner.phoneNumber ILIKE :${paramName}`,
								{
									[paramName]: `%${token}%`
								}
							)
							.orWhere(
								`patient.patientName ILIKE :${paramName}`,
								{
									[paramName]: `%${token}%`
								}
							)
							.orWhere(`doctor.firstName ILIKE :${paramName}`, {
								[paramName]: `%${token}%`
							})
							.orWhere(`doctor.lastName ILIKE :${paramName}`, {
								[paramName]: `%${token}%`
							});
					})
				);
			});
		}
		if (Array.isArray(doctors) && doctors.length > 0) {
			// Instead of filtering out non-matching doctors, use EXISTS to check if the appointment has any matching doctor
			query.andWhere(
				new Brackets(qb => {
					qb.where(
						`EXISTS (
							SELECT 1
							FROM appointment_doctors ad
							WHERE ad.appointment_id = appointment.id
							AND ad.clinic_user_id IN (:...doctors)
						)`,
						{ doctors }
					);
				})
			);
		}

		if (Array.isArray(status) && status.length > 0) {
			query.andWhere('status IN (:...status)', {
				status
			});
		}

		query
			.addSelect(
				`TO_TIMESTAMP(
				TO_CHAR(appointment.date::date, 'YYYY-MM-DD') || ' ' || TO_CHAR(appointment.start_time::time, 'HH24:MI:SS'),
				'YYYY-MM-DD HH24:MI:SS'
			)`,
				'sortable_timestamp' // Use a clear alias
			)
			.addOrderBy(
				'sortable_timestamp',
				orderBy === 'ASC' ? 'ASC' : 'DESC'
			)
			.skip((page - 1) * limit)
			.take(limit);

		const [appointments, total] = await query.getManyAndCount();

		const formattedAppointments = appointments
			.toSorted((appointment1, appointment2) => {
				const appointment1Date = moment(appointment1.date).format(
					'YYYY-MM-DD'
				);
				const appointment2Date = moment(appointment2.date).format(
					'YYYY-MM-DD'
				);
				const appointment1Time = moment(appointment1.startTime).format(
					'HH:mm:ss'
				);
				const appointment2Time = moment(appointment2.startTime).format(
					'HH:mm:ss'
				);
				const finalDate1 = moment(
					`${appointment1Date} ${appointment1Time}`,
					'YYYY-MM-DD HH:mm:ss'
				);
				const finalDate2 = moment(
					`${appointment2Date} ${appointment2Time}`,
					'YYYY-MM-DD HH:mm:ss'
				);

				if (finalDate1.isBefore(finalDate2)) {
					return -1;
				}
				if (finalDate1.isAfter(finalDate2)) {
					return 1;
				}
				return 0;
			})
			.map(appointment => {
				const formattedAppointment = {
					...appointment,
					weight: appointment.weight?.toString(),
					appointmentDoctors: appointment.appointmentDoctors.map(
						ad => ({
							id: ad.id,
							appointmentId: ad.appointmentId,
							doctorId: ad.clinicUser.id,
							primary: ad.primary,
							doctor: {
								id: ad.clinicUser.id,
								firstName: ad.clinicUser.user.firstName,
								lastName: ad.clinicUser.user.lastName,
								email: ad.clinicUser.user.email
							}
						})
					)
				};

				return formattedAppointment;
			});

		// If includeGoogleEvents is true, fetch and merge Google events
		let allAppointments = formattedAppointments;
		let totalCount = total;

		if (includeGoogleEvents) {
			try {
				this.logger.log(
					`[GoogleEvents] Fetching Google Calendar events for date: ${date}, clinic: ${clinicId}, doctors: ${JSON.stringify(doctors)}`
				);
				const googleEvents = await this.fetchGoogleEventsForDate(
					date,
					doctors,
					clinicId,
					viewerUserId
				);
				this.logger.log(
					`[GoogleEvents] Found ${googleEvents.length} Google Calendar events`
				);

				const existingGoogleEventKeys = new Set<string>();

				formattedAppointments.forEach(appt => {
					if (
						appt.googleEventId &&
						Array.isArray(appt.appointmentDoctors)
					) {
						appt.appointmentDoctors.forEach((doc: any) => {
							existingGoogleEventKeys.add(
								`${appt.googleEventId}_${doc.doctorId}`
							);
						});
					}
				});

				// Exclude Google events that correspond to the same doctor & event already present in Nidana
				const filteredGoogleEvents = googleEvents.filter(ev => {
					if (
						!ev.googleEventId ||
						!Array.isArray(ev.appointmentDoctors)
					)
						return true;
					const doctorId = ev.appointmentDoctors[0]?.doctorId;
					if (!doctorId) return true;
					return !existingGoogleEventKeys.has(
						`${ev.googleEventId}_${doctorId}`
					);
				});

				allAppointments = [
					...formattedAppointments,
					...filteredGoogleEvents
				];
				totalCount = total + filteredGoogleEvents.length;
				this.logger.log(
					`[GoogleEvents] Final result: ${formattedAppointments.length} Nidana appointments + ${filteredGoogleEvents.length} Google events = ${totalCount} total`
				);
			} catch (error) {
				this.logger.error(`Error fetching Google events:`, error);
				// Continue with just Nidana appointments if Google events fail
			}
		}

		this.logger.log('[AppointmentsService] getAllAppointments result', {
			date,
			googleEvents: totalCount - total,
			nidanaEvents: total,
			total: totalCount
		});

		return {
			appointments: allAppointments,
			total: totalCount
		};
	}

	async getAppointmentsForPatient(
		patientId: string,
		includeDeleted: boolean = false // Add optional parameter, default to false
	): Promise<AppointmentEntity[]> {
		const findOptions: FindManyOptions<AppointmentEntity> = {
			where: {
				patientId,
				type: Not(EnumAppointmentType.Impromptu) // Always filter out Impromptu type
			},
			relations: [
				'patient',
				'appointmentDoctors',
				'appointmentDoctors.clinicUser',
				'appointmentDoctors.clinicUser.user',
				'room',
				'appointmentDetails',
				'cart',
				'cart.invoice'
			],
			order: { date: 'DESC', startTime: 'DESC' }
		};

		if (includeDeleted) {
			findOptions.withDeleted = true;
		} else {
			(
				findOptions.where as FindOptionsWhere<AppointmentEntity>
			).deletedAt = IsNull();
		}

		const appointments = await this.appointmentRepository.find(findOptions);
		if (includeDeleted) {
			appointments.forEach(appointment => {
				if (appointment.deletedAt) {
					appointment.status = EnumAppointmentStatus.Cancelled;
				}
			});
		}

		// Create missing appointment details for appointments that don't have them
		const appointmentsWithDetails = await Promise.all(
			appointments.map(async appointment => {
				// If appointment details don't exist, create them with default structure
				if (!appointment.appointmentDetails) {
					this.logger.log(
						`Creating missing appointment details for appointment ${appointment.id}`,
						{
							appointmentId: appointment.id,
							patientId
						}
					);

					const defaultDetails =
						await this.generateDefaultTreatmentDetails(
							appointment.id,
							appointment.patientId
						);
					const createdAppointmentDetails =
						await this.appointmentDetailsRepository.save({
							appointmentId: appointment.id,
							details: defaultDetails
						});

					// Add the created appointment details to the appointment object
					appointment.appointmentDetails = createdAppointmentDetails;
				}

				return appointment;
			})
		);

		// Inject appointmentId into each appointment's details for the response
		const appointmentsWithInjectedDetails = await Promise.all(
			appointmentsWithDetails.map(appointment =>
				this.injectAppointmentIdIntoDetails(appointment)
			)
		);

		return appointmentsWithInjectedDetails;
	}

	/**
	 * Helper method to inject appointmentId into appointment details
	 * This adds appointmentId to the details object without saving it to the database
	 * If appointment details are null, it provides default treatment details
	 */
	private async injectAppointmentIdIntoDetails(
		appointment: any
	): Promise<any> {
		// Check if appointment details exist and are not null
		if (appointment?.appointmentDetails?.details) {
			// Create a copy of the details object and add appointmentId
			const detailsWithAppointmentId = {
				...appointment.appointmentDetails.details,
				appointmentId: appointment.id
			};

			// Return the appointment with modified details
			return {
				...appointment,
				appointmentDetails: {
					...appointment.appointmentDetails,
					details: detailsWithAppointmentId
				}
			};
		}

		// If appointment details are null or missing, provide default details
		const defaultDetails = await this.generateDefaultTreatmentDetails(
			appointment.id,
			appointment.patientId
		);

		return {
			...appointment,
			appointmentDetails: {
				id: null, // Will be null since no details record exists yet
				appointmentId: appointment.id,
				details: defaultDetails,
				createdAt: null,
				updatedAt: null
			}
		};
	}

	async getAppointmentDetails(appointmentId: string) {
		const appointmentDetail = await this.appointmentRepository
			.createQueryBuilder('appointment')
			.leftJoinAndSelect(
				'appointment.appointmentDoctors',
				'appointmentDoctors'
			)
			.leftJoinAndSelect('appointmentDoctors.clinicUser', 'clinicUser')
			.leftJoinAndSelect('clinicUser.user', 'doctor')
			.leftJoinAndSelect('appointment.room', 'room')
			.leftJoinAndSelect(
				'appointment.appointmentDetails',
				'appointmentDetails'
			)
			.where('appointment.id = :appointmentId', { appointmentId })
			.getOne();

		if (!appointmentDetail) {
			throw new NotFoundException(
				`Appointment details for appointment ID "${appointmentId}" not found`
			);
		}

		// If appointment details don't exist in the database, create them with default structure
		if (!appointmentDetail.appointmentDetails) {
			this.logger.log(
				`Creating missing appointment details for appointment ${appointmentId}`,
				{
					appointmentId
				}
			);

			const defaultDetails = await this.generateDefaultTreatmentDetails(
				appointmentId,
				appointmentDetail.patientId
			);
			const createdAppointmentDetails =
				await this.appointmentDetailsRepository.save({
					appointmentId: appointmentId,
					details: defaultDetails
				});

			// Add the created appointment details to the appointment object
			appointmentDetail.appointmentDetails = createdAppointmentDetails;
		}

		// Restructure the appointmentDoctors to maintain the previous format
		const restructuredAppointment = {
			...appointmentDetail,
			appointmentDoctors: appointmentDetail.appointmentDoctors.map(
				ad => ({
					id: ad.id,
					appointmentId: ad.appointmentId,
					doctorId: ad.clinicUser.id,
					primary: ad.primary,
					createdBy: ad.createdBy,
					updatedBy: ad.updatedBy,
					createdAt: ad.createdAt,
					updatedAt: ad.updatedAt,
					doctor: {
						id: ad.clinicUser.id,
						firstName: ad.clinicUser.user.firstName,
						lastName: ad.clinicUser.user.lastName,
						email: ad.clinicUser.user.email
					}
				})
			)
		};

		return restructuredAppointment;
	}

	async updateAppointmentDetails(
		id: string,
		updateAppointmentDetailsDto: UpdateAppointmentDetailsDto
	): Promise<{ status: boolean } | AppointmentDetailsEntity> {
		const lockKey = `appointment_update_lock:${id}`;
		const lockValue = `${Date.now()}_${Math.random()}`;
		const lockExpiry = 30; // 30 seconds lock expiry

		// Wait for lock with exponential backoff (prevents API failures)
		await this.waitForLockWithBackoff(lockKey, lockValue, lockExpiry, id);

		this.logger.log('Acquired distributed lock for appointment update', {
			appointmentId: id,
			lockKey,
			lockExpiry,
			callSiteId: updateAppointmentDetailsDto.callSiteId
		});

		try {
			// First, find the appointment to check its status
			const appointment = await this.appointmentRepository.findOne({
				where: { id }
			});

			if (!appointment) {
				throw new NotFoundException(
					`Appointment with ID "${id}" not found`
				);
			}

			// Validate that the appointment is in the Receiving Care state before allowing details update
			if (
				appointment.status === EnumAppointmentStatus.Scheduled ||
				appointment.status === EnumAppointmentStatus.Checkedin ||
				appointment.status === EnumAppointmentStatus.Cancelled ||
				appointment.status === EnumAppointmentStatus.Missed
			) {
				const currentStatus =
					appointment.status || EnumAppointmentStatus.Scheduled;
				this.logger.warn(
					`Attempted to update details for appointment ${id} while not in Receiving Care state. Current state: ${currentStatus}`,
					{ appointmentId: id, currentStatus }
				);
			}

			let appointmentDetails =
				await this.appointmentDetailsRepository.findOne({
					where: { appointmentId: id }
				});

			// If appointment details don't exist, create them with default structure
			if (!appointmentDetails) {
				const defaultDetails =
					await this.generateDefaultTreatmentDetails(
						id,
						appointment.patientId
					);
				appointmentDetails =
					await this.appointmentDetailsRepository.save({
						appointmentId: id,
						details: defaultDetails
					});
			}

			// Filter out null values from the update DTO to prevent saving null data
			const filteredUpdateDto = this.filterNullValues(
				updateAppointmentDetailsDto
			);

			// Only proceed with update if there are non-null values to update
			if (Object.keys(filteredUpdateDto).length === 0) {
				this.logger.warn(
					`No valid data to update for appointment ${id}`,
					{
						appointmentId: id,
						originalDto: updateAppointmentDetailsDto
					}
				);
				return { status: false };
			}

			Object.assign(appointmentDetails, filteredUpdateDto);
			const updateResult: UpdateResult =
				await this.appointmentDetailsRepository.update(
					appointmentDetails.id,
					{ details: appointmentDetails.details }
				);

			if (updateResult.affected === 0) {
				return { status: false };
			}

			const updatedAppointment =
				await this.appointmentDetailsRepository.findOne({
					where: { appointmentId: id }
				});

			// Emit socket updates based on call site ID if update was successful
			if (updatedAppointment && updateAppointmentDetailsDto.callSiteId) {
				await this.emitSocketUpdatesForCallSite(
					id,
					updateAppointmentDetailsDto.callSiteId,
					updatedAppointment.details
				);
			}

			this.logger.log(
				'Successfully completed appointment update with socket emission',
				{
					appointmentId: id,
					lockKey,
					callSiteId: updateAppointmentDetailsDto.callSiteId
				}
			);

			return updatedAppointment ?? { status: false };
		} catch (error) {
			this.logger.error('Error during appointment details update', {
				appointmentId: id,
				lockKey,
				error: error instanceof Error ? error.message : String(error)
			});
			throw error;
		} finally {
			// Always release the lock, regardless of success or failure
			// Use safe release to ensure only the lock owner can release it
			try {
				const released = await this.safeReleaseLock(lockKey, lockValue);
				if (released) {
					this.logger.log(
						'Released distributed lock for appointment update',
						{
							appointmentId: id,
							lockKey,
							lockValue: lockValue.substring(0, 10) + '...' // Log partial value for security
						}
					);
				} else {
					this.logger.warn(
						'Lock was already released or expired during processing',
						{
							appointmentId: id,
							lockKey
						}
					);
				}
			} catch (lockError) {
				this.logger.error('Failed to release distributed lock', {
					appointmentId: id,
					lockKey,
					error:
						lockError instanceof Error
							? lockError.message
							: String(lockError)
				});
			}
		}
	}

	/**
	 * Wait for distributed lock with exponential backoff
	 * This prevents API failures when another request is processing the same appointment
	 */
	private async waitForLockWithBackoff(
		lockKey: string,
		lockValue: string,
		lockExpiry: number,
		appointmentId: string,
		maxWaitTime: number = 25000, // Maximum wait time: 25 seconds
		initialDelay: number = 100 // Initial delay: 100ms
	): Promise<void> {
		const startTime = Date.now();
		let delay = initialDelay;
		let attempt = 1;

		while (Date.now() - startTime < maxWaitTime) {
			// Try to acquire the lock
			const lockAcquired = await this.redisService.setLock(
				lockKey,
				lockValue,
				lockExpiry
			);

			if (lockAcquired) {
				this.logger.log('Successfully acquired lock after waiting', {
					appointmentId,
					lockKey,
					attempt,
					waitTime: Date.now() - startTime
				});
				return;
			}

			// Check if lock still exists and get its TTL
			const lockExists = await this.redisService.exists(lockKey);
			if (!lockExists) {
				// Lock was released, try again immediately
				continue;
			}

			const ttl = await this.redisService.getTtl(lockKey);
			this.logger.log('Waiting for lock to be released', {
				appointmentId,
				lockKey,
				attempt,
				lockTtl: ttl,
				nextRetryIn: delay
			});

			// Wait with exponential backoff (max 2 seconds)
			await this.sleep(delay);
			delay = Math.min(delay * 1.5, 2000);
			attempt++;
		}

		// If we reach here, we've exceeded the maximum wait time
		this.logger.error('Failed to acquire lock within maximum wait time', {
			appointmentId,
			lockKey,
			maxWaitTime,
			attempts: attempt
		});

		throw new BadRequestException(
			'Unable to process appointment update at this time. The system is busy processing another update for this appointment. Please try again in a moment.'
		);
	}

	/**
	 * Safely release a distributed lock using Lua script
	 *
	 * WHY LUA IS NECESSARY:
	 * Prevents race condition where Process A's expired lock gets released
	 * after Process B acquires it, causing Process B to lose its lock unexpectedly.
	 *
	 * Example scenario without Lua:
	 * 1. Process A acquires lock: "appointment_123" = "timestamp_A"
	 * 2. Process A gets stuck (slow DB/network) for >30s
	 * 3. Lock expires, Process B acquires: "appointment_123" = "timestamp_B"
	 * 4. Process A finishes, calls DEL → deletes Process B's lock!
	 * 5. Result: Both processes think they have exclusive access
	 *
	 * With Lua: Process A's release fails (value mismatch), Process B protected.
	 * Critical for medical data integrity where race conditions = corrupted patient records.
	 */
	private async safeReleaseLock(
		lockKey: string,
		lockValue: string
	): Promise<boolean> {
		try {
			// Lua script to safely release lock only if value matches
			const luaScript = `
				if redis.call("GET", KEYS[1]) == ARGV[1] then
					return redis.call("DEL", KEYS[1])
				else
					return 0
				end
			`;

			// ioredis eval format: eval(script, keyCount, ...keys, ...args)
			const result = await this.redisService
				.getClient()
				.eval(luaScript, 1, lockKey, lockValue);

			return result === 1;
		} catch (error) {
			this.logger.error('Error in safe lock release', {
				lockKey,
				error: error instanceof Error ? error.message : String(error)
			});
			// Fallback to regular release if Lua script fails
			await this.redisService.releaseLock(lockKey);
			return true;
		}
	}

	/**
	 * Sleep utility function
	 */
	private sleep(ms: number): Promise<void> {
		return new Promise(resolve => setTimeout(resolve, ms));
	}

	/**
	 * Helper method to filter out null and undefined values from an object
	 * This prevents saving null data to the database
	 */
	private filterNullValues(obj: any): any {
		if (obj === null || obj === undefined) {
			return {};
		}

		const filtered: any = {};

		for (const [key, value] of Object.entries(obj)) {
			if (value !== null && value !== undefined) {
				// For nested objects, recursively filter null values
				if (typeof value === 'object' && !Array.isArray(value)) {
					const filteredNested = this.filterNullValues(value);
					if (Object.keys(filteredNested).length > 0) {
						filtered[key] = filteredNested;
					}
				} else {
					filtered[key] = value;
				}
			}
		}

		return filtered;
	}

	async updateAppointmentStatus(
		id: string,
		updateAppointmentDto: UpdateAppointmentsDto,
		invoiceId?: string
	): Promise<AppointmentEntity> {
		try {
			let appointment = await this.appointmentRepository.findOne({
				where: { id },
				relations: ['patient', 'appointmentDetails']
			});

			if (!appointment) {
				throw new NotFoundException(
					`Appointment with ID "${id}" not found`
				);
			}

			const currentStatus =
				appointment.status || EnumAppointmentStatus.Scheduled;
			const newStatus =
				updateAppointmentDto.status || EnumAppointmentStatus.Checkedin;

			if (!this.isValidStatusTransition(currentStatus, newStatus)) {
				throw new BadRequestException('Invalid status transition');
			}

			// Handle status-specific processing
			if (
				updateAppointmentDto.status === EnumAppointmentStatus.Completed
			) {
				await this.handleCompletedStatus(appointment, invoiceId);
			}

			await this.handleSoapPendingTask(appointment, updateAppointmentDto);

			// Update appointment status and timestamps
			appointment.status = updateAppointmentDto.status;
			appointment = this.updateAppointmentTimestamps(
				appointment,
				newStatus
			);

			// Save appointment status
			const updatedAppointment =
				await this.appointmentRepository.save(appointment);

			this.logger.log('Appointment status updated successfully', {
				appointmentId: id,
				newStatus: updateAppointmentDto.status
			});

			return updatedAppointment;
		} catch (error) {
			this.logger.error('Error in appointment status update', {
				appointmentId: id,
				error
			});

			if (
				error instanceof NotFoundException ||
				error instanceof BadRequestException
			) {
				throw error;
			}

			throw new InternalServerErrorException(
				'Failed to update appointment status'
			);
		}
	}

	convertToUTCDate = (date: Date): Date => {
		return moment(date).utc().toDate();
	};

	private updateAppointmentTimestamps(
		appointment: AppointmentEntity,
		newStatus: EnumAppointmentStatus
	): AppointmentEntity {
		return updateTimestamps(appointment, newStatus);
	}

	private async handleSoapPendingTask(
		appointment: AppointmentEntity,
		updateAppointmentDto: UpdateAppointmentsDto
	): Promise<void> {
		if (
			updateAppointmentDto.status !== EnumAppointmentStatus.Checkedout ||
			!updateAppointmentDto.soapPending
		) {
			return;
		}

		try {
			const createTaskDto = new CreateTaskDto();
			createTaskDto.isCompleted = false;
			createTaskDto.title = `SOAP Pending for ${appointment.patient.patientName} who had an appointment on ${appointment.date.toDateString()}`;
			createTaskDto.userId = updateAppointmentDto.userId as string;

			await this.tasksService.createTask(createTaskDto);
		} catch (error) {
			this.logger.error('Error creating SOAP pending task:', error);
			// Continue execution
		}
	}

	private extractAppointmentData(appointmentDetails: any): {
		plans: any[];
		prescriptions: any[];
		assessmentList: any[];
	} {
		if (!appointmentDetails?.details) {
			return { plans: [], prescriptions: [], assessmentList: [] };
		}

		const details = appointmentDetails.details;
		const plans = details.plans?.list || [];
		const prescriptions = details.prescription?.list || [];
		const assessmentList = details.assessment?.list || [];

		this.logger.log('Extracted appointment data', {
			context: 'Appointments',
			plansCount: plans.length,
			prescriptionsCount: prescriptions.length,
			assessmentsCount: assessmentList.length
		});

		return { plans, prescriptions, assessmentList };
	}

	private async processItemReminders(
		appointment: AppointmentEntity,
		plansAndPrescriptions: any[]
	): Promise<void> {
		if (plansAndPrescriptions.length === 0) {
			return;
		}

		try {
			// Get all pending reminders
			const remindersResponse = await this.remindersService.findAll(
				appointment.patientId,
				ReminderStatus.PENDING
			);

			const reminderUpdates: Promise<any>[] = [];

			// Process each plan/prescription
			for (const item of plansAndPrescriptions) {
				// Find matching reminders
				const matchingReminders = remindersResponse.reminders.filter(
					reminder =>
						reminder.inventoryItemId === item.prescriptionId ||
						reminder.inventoryItemId === item.planId
				);

				// Process each matching reminder
				for (const reminder of matchingReminders) {
					const updatePromise = this.remindersService
						.completeReminder(reminder, 1, appointment?.id)
						.catch(error => {
							this.logger.error('Error completing reminder', {
								reminderId: reminder.id,
								error
							});
							return null;
						});

					reminderUpdates.push(updatePromise);
				}
			}

			// Wait for all updates to complete
			const results = await Promise.allSettled(reminderUpdates);

			// Log results
			const successful = results.filter(
				r => r.status === 'fulfilled'
			).length;
			const failed = results.filter(r => r.status === 'rejected').length;
			this.logger.log('Reminder completion summary:', {
				total: results.length,
				successful,
				failed
			});
		} catch (error) {
			this.logger.error('Error processing reminders:', error);
		}
	}

	private async processGlobalReminders(
		appointment: AppointmentEntity,
		plans: any[],
		assessmentList: any[],
		prescriptions: any[],
		invoiceId?: string
	): Promise<void> {
		try {
			this.logger.log('Starting global reminders processing', {
				context: 'Appointments',
				appointmentId: appointment.id,
				patientId: appointment.patientId,
				clinicId: appointment.clinicId
			});

			await this.globalReminderService.processAppointmentTriggers(
				appointment.id,
				appointment.patientId,
				appointment.clinicId,
				appointment.brandId,
				plans,
				assessmentList,
				prescriptions,
				invoiceId
			);

			this.logger.log('Global reminders processed successfully', {
				context: 'Appointments',
				appointmentId: appointment.id
			});
		} catch (error) {
			this.logger.error('Error processing global reminders:', {
				context: 'Appointments',
				error,
				appointmentId: appointment.id
			});
		}
	}

	private async handleCompletedStatus(
		appointment: AppointmentEntity,
		invoiceId?: string
	): Promise<void> {
		const appointmentDetails =
			await this.appointmentDetailsRepository.findOne({
				where: { appointmentId: appointment.id }
			});

		if (!appointmentDetails?.details) {
			return;
		}

		const { plans, prescriptions, assessmentList } =
			this.extractAppointmentData(appointmentDetails);

		// Combine plans and prescriptions for reminder processing
		const plansAndPrescriptions = [...plans, ...prescriptions];

		// Process item-specific reminders
		await this.processItemReminders(appointment, plansAndPrescriptions);

		// Process global reminders
		await this.processGlobalReminders(
			appointment,
			plans,
			assessmentList,
			prescriptions,
			invoiceId
		);
	}

	async updateAppointment(
		id: string,
		updateAppointmentFieldsDto: UpdateAppointmentFeildsDto
	): Promise<AppointmentEntity> {
		let appointment = await this.appointmentRepository.findOne({
			where: { id },
			relations: [
				'patient',
				'patient.patientOwners',
				'patient.patientOwners.ownerBrand',
				'patient.patientOwners.ownerBrand.globalOwner',
				'clinic',
				'clinic.brand'
			]
		});

		if (!appointment) {
			throw new NotFoundException(
				`Appointment with ID "${id}" not found`
			);
		}

		// Store previous appointment data for availability update
		const previousAppointment = {
			date: appointment.date,
			startTime: appointment.startTime,
			endTime: appointment.endTime
		};

		await this.appointmentDoctorsRepository.delete({
			appointmentId: appointment.id
		});

		const doctorPromises = updateAppointmentFieldsDto.doctorIds.map(
			async doctorId => {
				await this.appointmentDoctorsRepository.save({
					appointmentId: appointment?.id,
					doctorId: doctorId,
					primary: true
				});
			}
		);

		const providerPromises = updateAppointmentFieldsDto.providerIds.map(
			async doctorId => {
				await this.appointmentDoctorsRepository.save({
					appointmentId: appointment?.id,
					doctorId: doctorId,
					primary: false
				});
			}
		);

		await Promise.all([...doctorPromises, ...providerPromises]);

		// Remove status from update DTO - status updates should only go through dedicated endpoint
		const { status, ...fieldsWithoutStatus } = updateAppointmentFieldsDto;

		appointment = {
			...appointment,
			...fieldsWithoutStatus
		};

		if (updateAppointmentFieldsDto.status) {
			appointment = updateTimestamps(
				appointment,
				updateAppointmentFieldsDto.status
			);
		}

		const savedAppointment =
			await this.appointmentRepository.save(appointment);

		try {
			// Update appointment date to UTC
			appointment.date = this.convertToUTCDate(appointment.date);

			// Load complete appointment with required relations for availability update
			const appointmentWithDoctors =
				await this.appointmentRepository.findOne({
					where: { id: appointment.id },
					relations: [
						'appointmentDoctors',
						'appointmentDoctors.clinicUser'
					]
				});

			if (!appointmentWithDoctors) {
				this.logger.error(
					'Could not find appointment with relations for availability update',
					{
						appointmentId: appointment.id
					}
				);
				return savedAppointment;
			}

			appointmentWithDoctors.date = this.convertToUTCDate(
				appointmentWithDoctors.date
			);
			previousAppointment.date = this.convertToUTCDate(
				previousAppointment.date
			);

			await this.availabilityService.handleAppointmentChange(
				appointmentWithDoctors,
				'update',
				previousAppointment
			);
		} catch (error) {
			this.logger.error(
				'Error updating availability after updating appointment',
				{
					appointmentId: appointment.id,
					error
				}
			);
		}

		// Sync with Google Calendar
		this.syncOnAppointmentUpdate(savedAppointment);

		try {
			// Reload full appointment with patient owners & clinic relations for notifications
			const appointmentWithRelations =
				await this.appointmentRepository.findOne({
					where: { id: savedAppointment.id },
					relations: [
						'patient',
						'patient.patientOwners',
						'patient.patientOwners.ownerBrand',
						'patient.patientOwners.ownerBrand.globalOwner',
						'clinic',
						'clinic.brand'
					]
				});
			if (appointmentWithRelations) {
				await this.sendAppointmentNotifications(
					appointmentWithRelations,
					'update'
				);
			}
		} catch (notifyErr) {
			this.logger.error('Failed to send update notifications', {
				appointmentId: savedAppointment.id,
				notifyErr
			});
		}

		return savedAppointment;
	}

	/**
	 * Synchronize appointment update with Google Calendar
	 */
	private async syncOnAppointmentUpdate(
		appointment: AppointmentEntity
	): Promise<void> {
		try {
			// Reload appointment with necessary relations (including doctors)
			const appointmentWithRelations =
				await this.appointmentRepository.findOne({
					where: { id: appointment.id },
					relations: [
						'patient',
						'clinic',
						'appointmentDoctors',
						'appointmentDoctors.clinicUser',
						'appointmentDoctors.clinicUser.user'
					]
				});

			if (!appointmentWithRelations) {
				return;
			}

			let user: User | null = null;

			// 1. Prefer a doctor attached to the appointment who has Google Calendar connected
			for (const doc of appointmentWithRelations.appointmentDoctors ??
				[]) {
				const docUser = doc?.clinicUser?.user;
				if (
					docUser &&
					docUser.isGoogleSyncEnabled &&
					docUser.googleCalendarRefreshToken &&
					docUser.googleCalendarId
				) {
					user = docUser;
					break;
				}
			}

			// No doctor / updater user with Google Calendar – skip sync
			if (!user) {
				return;
			}

			if (
				user &&
				user.isGoogleSyncEnabled &&
				user.googleCalendarRefreshToken &&
				user.googleCalendarId
			) {
				this.logger.log(
					`Syncing updated appointment ${appointment.id} with Google Calendar for user ${user.id}`
				);

				const googleEventId =
					await this.googleCalendarService.updateEvent(
						user.id,
						appointmentWithRelations
					);

				// If an event was newly created during the update, persist the ID
				if (googleEventId && !appointment.googleEventId) {
					await this.appointmentRepository.update(appointment.id, {
						googleEventId
					});
				}
			}
		} catch (error) {
			this.logger.error(
				`Failed to sync updated appointment ${appointment.id} with Google Calendar:`,
				error
			);
		}
	}

	/**
	 * Fetch Google Calendar events for a specific date and convert them to appointment format
	 */
	private async fetchGoogleEventsForDate(
		date: string,
		doctors: Array<any> = [],
		clinicId: string,
		viewerUserId?: string
	): Promise<any[]> {
		// New implementation: query local cache instead of calling Google API per user
		try {
			this.logger.log(
				`[GoogleEventsDebug] Starting fetch for date: ${date}, clinic: ${clinicId}`
			);
			if (!date) return [];

			// Normalize doctor filter array
			let parsedDoctors: string[] = Array.isArray(doctors)
				? (doctors as string[])
				: [];
			if (typeof doctors === 'string') {
				try {
					parsedDoctors = JSON.parse(doctors);
				} catch {
					parsedDoctors = [];
				}
			}
			this.logger.log(
				`[GoogleEventsDebug] Parsed doctor filter: ${JSON.stringify(parsedDoctors)}`
			);

			// Get all users in clinic with sync enabled, apply doctor filter if any
			const usersQb = this.userRepository
				.createQueryBuilder('user')
				.innerJoin('user.clinicUsers', 'cu')
				.where('cu.clinicId = :clinicId', { clinicId })
				.andWhere('user.isGoogleSyncEnabled = true')
				.andWhere('user.googleCalendarId IS NOT NULL')
				.leftJoinAndSelect('user.clinicUsers', 'clinicUsers');

			if (parsedDoctors.length > 0) {
				usersQb.andWhere('cu.id IN (:...docs)', {
					docs: parsedDoctors
				});
			}

			const users = await usersQb.getMany();
			this.logger.log(
				`[GoogleEventsDebug] Found ${users.length} users with Google Calendar sync enabled in clinic ${clinicId}`
			);
			if (users.length === 0) {
				this.logger.warn(
					`[GoogleEventsDebug] No users found with Google Calendar sync enabled for clinic ${clinicId}`
				);
				return [];
			}

			const startDate = new Date(date);
			startDate.setHours(0, 0, 0, 0);
			const endDate = new Date(date);
			endDate.setHours(23, 59, 59, 999);
			this.logger.log(
				`[GoogleEventsDebug] Querying cache with date range: ${startDate.toISOString()} to ${endDate.toISOString()}`
			);

			const googleEvents: any[] = [];
			const processedIds = new Set<string>();

			for (const user of users) {
				this.logger.log(
					`[GoogleEventsDebug] Checking cache for user ${user.email} (${user.id})`
				);
				const cached = await this.googleCalendarCache.getEventsRange(
					user.id,
					startDate,
					endDate
				);
				this.logger.log(
					`[GoogleEventsDebug] User ${user.email} (${user.id}) has ${cached.length} cached events for ${date}`
				);

				if (cached.length === 0) continue;

				for (const ev of cached) {
					this.logger.log(
						`[GoogleEventsDebug] Processing event: ${ev.eventId}, Summary: ${ev.summary}, Status: ${ev.status}`
					);
					// Skip events that originated from Nidana (they carry a nidanaAppointmentId property)
					if (
						ev.raw?.extendedProperties?.private?.nidanaAppointmentId
					) {
						this.logger.log(
							`[GoogleEventsDebug] Skipping event ${ev.eventId} because it originated from Nidana.`
						);
						continue;
					}
					if (ev.status === 'cancelled') {
						this.logger.log(
							`[GoogleEventsDebug] Skipping event ${ev.eventId} because it is cancelled.`
						);
						continue;
					}

					const clinicUser = user.clinicUsers?.find(
						cu => cu.clinicId === clinicId
					);
					if (!clinicUser) {
						this.logger.log(
							`[GoogleEventsDebug] Skipping event ${ev.eventId} because no matching clinicUser was found.`
						);
						continue;
					}

					const dedupKey = `${ev.eventId}_${clinicUser.id}`;
					if (processedIds.has(dedupKey)) {
						this.logger.log(
							`[GoogleEventsDebug] Skipping event ${ev.eventId} due to duplicate key: ${dedupKey}`
						);
						continue;
					}

					const isViewerOwner =
						viewerUserId && user.id === viewerUserId;
					const safeSummary = isViewerOwner
						? ev.summary || 'Google Calendar Event'
						: 'Busy';
					const safeDescription = isViewerOwner
						? ev.description || ''
						: '';

					this.logger.log(
						`[GoogleEventsDebug] Adding event ${ev.eventId} to the list.`
					);
					googleEvents.push({
						id: `google_${dedupKey}`,
						googleEventId: ev.eventId, // keep original eventId for reference
						patientId: null,
						clinicId,
						date: ev.startTime,
						startTime: ev.startTime,
						endTime: ev.endTime,
						status: ev.status || 'Scheduled',
						type: 'Google Event',
						reason: safeSummary,
						notes: safeDescription,
						weight: null,
						triage: null,
						deletedAt: null,
						createdAt: ev.createdAt,
						updatedAt: ev.updatedAt,
						isBlocked: false,
						brandId: null,
						mode: 'Google',
						patient: {
							patientName: safeSummary,
							patientOwners: []
						},
						appointmentDoctors: [
							{
								id: null,
								appointmentId: null,
								doctorId: clinicUser.id,
								primary: true,
								doctor: {
									id: clinicUser.id,
									firstName: user.firstName,
									lastName: user.lastName,
									email: user.email
								}
							}
						]
					});

					processedIds.add(dedupKey);
				}
			}

			this.logger.log(
				`[GoogleEventsDebug] Finished processing all users. Total Google events found: ${googleEvents.length}`
			);
			return googleEvents;
		} catch (error) {
			this.logger.error(
				'[GoogleEventsDebug] Failed to fetch cached Google events:',
				error
			);
			return [];
		}
	}

	/**
	 * Convert a Google Calendar event to appointment format
	 */
	private convertGoogleEventToAppointment(
		event: any,
		user: any,
		clinicId: string,
		viewerUserId?: string
	): any | null {
		try {
			// Ignore cancelled events
			if (event.status === 'cancelled') {
				return null;
			}

			if (!event.start?.dateTime || !event.end?.dateTime) {
				return null;
			}

			const startTime = new Date(event.start.dateTime);
			const endTime = new Date(event.end.dateTime);

			// Find the correct clinic user for this clinic
			const clinicUser = user.clinicUsers?.find(
				(cu: ClinicUser) => cu.clinicId === clinicId
			);

			// If the user is not mapped to this clinic, skip showing this event to prevent it
			// from appearing under an unrelated doctor's schedule.
			if (!clinicUser) {
				return null;
			}

			const clinicUserId = clinicUser.id;

			// Determine visibility of event details
			const isViewerOwner = viewerUserId && user.id === viewerUserId;
			const safeSummary = isViewerOwner
				? event.summary || 'Google Calendar Event'
				: 'Busy';
			const safeDescription = isViewerOwner
				? event.description || ''
				: '';

			const convertedAppointment = {
				id: `google_${event.id}`,
				googleEventId: event.id,
				patientId: null,
				clinicId: clinicId,
				date: startTime,
				startTime: startTime,
				endTime: endTime,
				status:
					event.status === 'cancelled' ? 'Cancelled' : 'Scheduled',
				type: 'Google Event',
				reason: safeSummary,
				notes: safeDescription,
				weight: null,
				triage: null,
				deletedAt: null,
				createdAt: new Date(event.created || event.updated),
				updatedAt: new Date(event.updated),
				checkinTime: null,
				receivingCareTime: null,
				checkoutTime: null,
				isBlocked: false,
				brandId: null,
				mode: 'Google',
				patient: {
					id: null,
					patientName: safeSummary,
					patientOwners: [
						{
							ownerBrand: {
								firstName: 'Google',
								lastName: 'Calendar',
								globalOwner: {
									phoneNumber: ''
								}
							}
						}
					]
				},
				appointmentDoctors: [
					{
						id: null,
						appointmentId: null,
						doctorId: clinicUserId,
						primary: true,
						doctor: {
							id: clinicUserId,
							firstName: user.firstName,
							lastName: user.lastName,
							email: user.email
						}
					}
				],
				room: null
			};

			return convertedAppointment;
		} catch (error) {
			this.logger.error(
				`Failed to convert Google event ${event.id} to appointment format:`,
				error
			);
			return null;
		}
	}

	private isValidStatusTransition(
		currentStatus: EnumAppointmentStatus,
		newStatus: EnumAppointmentStatus
	): boolean {
		// Define valid transitions
		const validTransitions: {
			[key in EnumAppointmentStatus]?: EnumAppointmentStatus[];
		} = {
			[EnumAppointmentStatus.Scheduled]: [
				EnumAppointmentStatus.Checkedin,
				EnumAppointmentStatus.Cancelled
			],
			[EnumAppointmentStatus.Checkedin]: [
				EnumAppointmentStatus.ReceivingCare,
				EnumAppointmentStatus.Cancelled
			],
			[EnumAppointmentStatus.ReceivingCare]: [
				EnumAppointmentStatus.Checkedout,
				EnumAppointmentStatus.Cancelled
			],
			[EnumAppointmentStatus.Checkedout]: [
				EnumAppointmentStatus.Completed,
				EnumAppointmentStatus.Cancelled
			],
			[EnumAppointmentStatus.Completed]: [],
			[EnumAppointmentStatus.Cancelled]: []
		};

		const allowedStatuses = validTransitions[currentStatus];
		return allowedStatuses ? allowedStatuses.includes(newStatus) : false;
	}

	async deleteAppointment(id: string) {
		const appointment = await this.appointmentRepository.findOne({
			where: { id },
			relations: [
				'patient',
				'patient.patientOwners',
				'patient.patientOwners.ownerBrand',
				'patient.patientOwners.ownerBrand.globalOwner',
				'appointmentDoctors',
				'appointmentDoctors.clinicUser',
				'appointmentDoctors.clinicUser.user',
				'clinic',
				'clinic.brand'
			]
		});

		if (!appointment) {
			throw new NotFoundException(
				`This appointment with ${id} doesn't exist`
			);
		}

		// Sync with Google Calendar before deleting - use appointment doctors like create/update
		if (appointment.googleEventId) {
			// Find a doctor attached to the appointment who has Google Calendar connected
			let user: User | null = null;
			for (const doc of appointment.appointmentDoctors ?? []) {
				const docUser = doc?.clinicUser?.user;
				if (
					docUser &&
					docUser.isGoogleSyncEnabled &&
					docUser.googleCalendarRefreshToken &&
					docUser.googleCalendarId
				) {
					user = docUser;
					break;
				}
			}

			if (user) {
				await this.googleCalendarService.deleteEvent(
					user.id,
					appointment.googleEventId
				);
			}
		}

		appointment.deletedAt = new Date();
		const updatedAppointment = await this.appointmentRepository.update(id, {
			deletedAt: appointment.deletedAt
		});

		// Handle availability
		try {
			// Extract local date parts to preserve the correct calendar date
			const localDate = new Date(appointment.date);
			const year = localDate.getFullYear();
			const month = localDate.getMonth();
			const day = localDate.getDate();

			// Create UTC date to preserve the exact calendar date
			appointment.date = new Date(Date.UTC(year, month, day));

			await this.availabilityService.handleAppointmentChange(
				appointment,
				'delete'
			);
		} catch (error) {
			this.logger.error(
				'Error updating availability after deleting appointment',
				{
					appointmentId: id,
					error
				}
			);
			// Continue with deletion even if availability update fails
		}

		// Send cancellation notifications
		try {
			await this.sendAppointmentNotifications(appointment, 'cancel');
		} catch (notifyErr) {
			this.logger.error('Failed to send cancellation notifications', {
				appointmentId: id,
				notifyErr
			});
		}

		return { status: true };
	}

	async checkPatientOnGoingAppointment(patientId: string): Promise<{
		hasOngoingAppointment: boolean;
		appointment: AppointmentEntity | null;
	}> {
		const appointment = await this.appointmentRepository.findOne({
			where: {
				patientId,
				status: In([
					EnumAppointmentStatus.Checkedin,
					EnumAppointmentStatus.ReceivingCare,
					EnumAppointmentStatus.Checkedout
				])
				//date: new Date()
			}
		});

		if (!appointment) {
			return { hasOngoingAppointment: false, appointment: null };
		}

		return { hasOngoingAppointment: true, appointment };
	}

	async downloadTodaysAppointment(
		clinicId: string,
		date: string
	): Promise<string> {
		this.logger.log(
			'service called for downloading todays appointmentList',
			date
		);
		const customDate = moment(date).add(5, 'hours').add(30, 'minutes');
		const limit = 100;
		try {
			const response = await this.getAllAppointments(
				1,
				limit,
				'DESC',
				customDate.format('YYYY-MM-DD'),
				'',
				[],
				[],
				false,
				clinicId
			);

			this.logger.log('appointmentResponse', response);
			if (response && response.appointments.length > 0) {
				const appointmentDetailsList = response?.appointments.map(
					list => {
						let patientAge: string = '';
						if (list?.patient?.age) {
							const age = calculateAge(list?.patient?.age);
							patientAge = `${age} yrs`;
						}
						return {
							timeStart:
								moment(list?.startTime)
									.add(5, 'hours')
									.add(30, 'minutes')
									.format('LT') || '',
							timeEnd:
								moment(list?.endTime)
									.add(5, 'hours')
									.add(30, 'minutes')
									.format('LT') || '',
							petName: list?.patient?.patientName || '',
							petBreed: list?.patient?.breed || '',
							petAge: patientAge || '',
							ownerName: `${list?.patient?.patientOwners[0]?.ownerBrand?.firstName} ${list?.patient?.patientOwners[0]?.ownerBrand?.lastName}`,
							ownerPhone: `+${list?.patient?.patientOwners[0]?.ownerBrand?.globalOwner?.countryCode || ''} ${list?.patient?.patientOwners[0]?.ownerBrand?.globalOwner?.phoneNumber || ''}`,
							doctorName: `Dr. ${list?.appointmentDoctors[0]?.doctor.firstName || ''} ${list?.appointmentDoctors[0]?.doctor?.lastName}`,
							room: list?.room?.name || '',
							visitType: list?.type || '',
							treatment: list?.reason || ''
						};
					}
				);
				this.logger.log(
					'latest appointmentList',
					appointmentDetailsList
				);

				const data = {
					appointments: appointmentDetailsList,
					date: moment(date).format('DD/MM/YYYY')
				};
				const html = generateAppointmentSummary(
					data?.date,
					data.appointments
				);
				const pdfBuffer = await generatePDF(html);
				const outputPath = path.resolve('output.pdf');
				await fs.writeFile(outputPath, pdfBuffer);
				this.logger.log('document created succesfully');

				return outputPath;
			} else {
				this.logger.log('No appointments found for today');
				throw new Error('No appointments found.');
			}
		} catch (error) {
			this.logger.error(
				'Error fetching appointments for PDF generation',
				{ error }
			);
			throw error;
		}
	}

	/**
	 * Cron job that runs every hour to check for missed appointments
	 * Finds appointments that are:
	 * 1. Not in Completed, Cancelled, or Missed status
	 * 2. More than 16 hours old (based on combined date and start time)
	 * And marks them as Missed
	 */
	@Cron('0 * * * *')
	async handleMissedAppointments() {
		try {
			this.logger.log('Starting missed appointments check');

			// Calculate the cutoff time (16 hours ago)
			const cutoffTime = moment().subtract(16, 'hours').toDate();

			// Find appointments that meet our criteria using combined date and time
			const missedAppointments = await this.appointmentRepository
				.createQueryBuilder('appointment')
				.where('appointment.deletedAt IS NULL')
				.andWhere(
					`make_timestamp(
						EXTRACT(YEAR FROM appointment.date)::int,
						EXTRACT(MONTH FROM appointment.date)::int,
						EXTRACT(DAY FROM appointment.date)::int,
						EXTRACT(HOUR FROM appointment.start_time)::int,
						EXTRACT(MINUTE FROM appointment.start_time)::int,
						EXTRACT(SECOND FROM appointment.start_time)::double precision
					) < :cutoffTime`,
					{ cutoffTime }
				)
				.andWhere('appointment.status IN (:...statuses)', {
					statuses: [EnumAppointmentStatus.Scheduled]
				})
				.getMany();

			if (missedAppointments.length === 0) {
				this.logger.log('No missed appointments found');
				return;
			}

			this.logger.log(
				`Found ${missedAppointments.length} missed appointments`
			);

			// Update all found appointments to Missed status
			const updatePromises = missedAppointments.map(async appointment => {
				try {
					appointment.status = EnumAppointmentStatus.Missed;
					await this.appointmentRepository.save(appointment);

					this.logger.log(
						`Updated appointment ${appointment.id} to Missed status`
					);
				} catch (error) {
					this.logger.error(
						`Failed to update appointment ${appointment.id}`,
						error
					);
				}
			});

			await Promise.all(updatePromises);

			this.logger.log(
				`Successfully processed ${missedAppointments.length} missed appointments`
			);
		} catch (error) {
			this.logger.error(
				'Error in handleMissedAppointments cron job',
				error
			);
		}
	}

	async createImpromptuAppointment(data: {
		clinicId: string;
		patientId: string;
		brandId: string;
		date: Date;
		startTime: Date;
		endTime: Date;
		reason: string;
		type: EnumAppointmentType;
		status: EnumAppointmentStatus;
		createdBy?: string;
	}): Promise<AppointmentEntity> {
		// Create appointment record
		const appointment = this.appointmentRepository.create({
			...data
		});

		const createdAppointment =
			await this.appointmentRepository.save(appointment);

		// Create appointment details record with default JSONB structure
		const defaultDetails = {
			plans: { list: [], notes: '' },
			followup: null,
			objective: {
				vitals: [],
				bodyMaps: [],
				labReports: [],
				physicalExam: [
					{
						id: '05bb1078-ea01-407b-bad7-6933dc85b196',
						notes: '',
						status: '',
						category: 'Oral cavity/Teeth'
					},
					{
						id: 'f7091c98-0e83-47af-89d0-8a74959ac555',
						notes: '',
						status: '',
						category: 'Eyes/orbit'
					},
					{
						id: 'c4fcdff2-4f5b-4743-bbb8-fd92416dbbf6',
						notes: '',
						status: '',
						category: 'Throat'
					},
					{
						id: '60f0b78f-44c4-4368-80a1-587f3196eec5',
						notes: '',
						status: '',
						category: 'Respiratory'
					},
					{
						id: '78ae866b-ea47-4fed-986a-71058afde0b3',
						notes: '',
						status: '',
						category: 'Musculoskeletal'
					},
					{
						id: 'b6a2d620-6d89-4447-89fc-668f23c19906',
						notes: '',
						status: '',
						category: 'Urogenital'
					},
					{
						id: '64c8facb-54b2-4510-986a-5f8791f5a88a',
						notes: '',
						status: '',
						category: 'Mucous membranes'
					},
					{
						id: '3d2989b0-8692-422b-bb65-9a946117cee4',
						notes: '',
						status: '',
						category: 'Ears'
					},
					{
						id: '3f599bd8-6c58-45a8-88a1-29d2ac606dd3',
						notes: '',
						status: '',
						category: 'Cardio vascular'
					},
					{
						id: 'c268247e-ab34-407b-8837-aef7fd2bef6c',
						notes: '',
						status: '',
						category: 'Abdomen'
					},
					{
						id: '8cadee67-8af4-4c0f-bcaf-5191e0fc5a07',
						notes: '',
						status: '',
						category: 'Glands/Lymph Nodes'
					},
					{
						id: 'ad562754-fd3e-413e-8f7c-add2dd3ff3ae',
						notes: '',
						status: '',
						category: 'Rectal'
					}
				],
				ultrasoundExam: [
					{
						id: '59452a26-dd76-482f-823e-8910e30463e3',
						notes: 'The liver is regular in size with a homogenous echo pattern throughout the parenchyma.\n        The liver margins are well-defined and smooth.\n        No focal lesion or intrahepatic duct dilatation evident.',
						status: '',
						category: 'Liver'
					},
					{
						id: 'a94daca9-b5cc-4c0b-8388-c0849ce80955',
						notes: 'The portal vein is patent, demonstrates hepatopetal flow with a velocity of **___ cm/s**.\n        Hepatic veins demonstrate normal calibre and regular configuration.',
						status: '',
						category: 'Portal Vein'
					},
					{
						id: 'a449583d-d6af-4e5b-9d1c-78c23dc3573a',
						notes: 'Normal distended gallbladder with a uniform thin wall.\n        Sludge/Choleliths not seen within the dependent portion of the gallbladder.\n        The common duct measurement was normal at the level of the main portal branch.',
						status: '',
						category: 'Gallbladder'
					},
					{
						id: 'd79be8b6-437e-4225-b497-80370c4da79a',
						notes: 'Regular sized and shaped spleen with a normal homogenous parenchymal echo pattern.\n        No evidence of splenic disease.',
						status: '',
						category: 'Spleen'
					},
					{
						id: '5f14af57-fde1-4225-a70a-e13ee66c84cf',
						notes: 'Where visualised, the pancreas is normal in size and echotexture with no evidence of pancreatic disease.',
						status: '',
						category: 'Pancreas'
					},
					{
						id: 'd9238510-38ce-4254-af3d-fa3e65732496',
						notes: 'The stomach is empty at the time of the scan.\n        Normal wall layer architecture and gastric wall thickness (**___ mm**).\n        No obvious foreign body seen within the contracted rugal folds.',
						status: '',
						category: 'Stomach'
					},
					{
						id: '9d83587b-61d6-47ce-8c78-a33fd897009c',
						notes: 'Duodenum demonstrates normal overall wall thickness (**___ mm**) and characteristic wall layer echo pattern.\n        Remaining small intestines are normal with no evidence of foreign body or disease.',
						status: '',
						category: 'Small Intestines'
					},
					{
						id: 'e2f43078-4fe1-461d-93b9-a482bc6162d8',
						notes: 'The colon contains air/faecal content/fluid at the time of scan.\n        Wall layering and overall thickness appear normal.\n        ICCJ identified as normal.',
						status: '',
						category: 'Colon'
					},
					{
						id: '57a25fd0-7c6d-4a3b-9d6a-259046fcefff',
						notes: 'Both kidneys are normal in size, shape, and \n        echogenicity with cortico-medullary differentiation. \n        There is no evidence of pyelectasia or calculi. \n        Left kidney: **___ mm**, Right kidney: **___ mm** (long axis).',
						status: '',
						category: 'Kidneys'
					},
					{
						id: '94fe8ba4-d284-4f07-9ba8-e93a4306a775',
						notes: 'Normal in size, echogenicity, and characteristic shape\n        Left adrenal: **___ mm**, Right adrenal: **___ mm** (caudal pole).',
						status: '',
						category: 'Adrenal Glands'
					},
					{
						id: 'ac2bc911-d247-45bd-84b5-c1d1667062ba',
						notes: 'No evidence of dilation or thrombus noted.',
						status: '',
						category: 'Aorta'
					},
					{
						id: '27cc51cb-d247-4f53-ae08-2cbe6ec8f77c',
						notes: 'No evidence of thrombus or other abnormalities.',
						status: '',
						category: 'Caudal Vena Cava (CVC)'
					},
					{
						id: '7f4409c2-b0b2-4c85-947f-b4f8c0463ba5',
						notes: 'Moderately filled at time of scan.\n        Smooth, uniform wall thickness within normal limits for fill status (**___ mm**).\n        Trigone region did not show any abnormalities.',
						status: '',
						category: 'Urinary Bladder'
					},
					{
						id: '073efb8b-bdb1-47c4-897e-8e94a88d7e63',
						notes: 'Prostate gland is normal in size, shape, and echotexture\n        Bilobar measurements: **___ mm x ___ mm**.\n        Prostate volume: **___ ml\n        Testes: Normal shape, size, and echotexture (if intact).\n        ',
						status: '',
						category: 'Prostate Gland / Testes'
					},
					{
						id: '01d2bf00-36e8-4041-b1b4-349d6e1014fb',
						notes: '**Neutered/Intact status:** _______________.\n        Uterus and ovaries could not be visualized.',
						status: '',
						category: 'Uterus / Ovaries'
					},
					{
						id: '7ece491d-80d7-4b98-94b7-dca7e3f32e69',
						notes: 'LMILN: **___ x ___ mm**, RMILN: **___ x ___ mm**.\n        No gross lymphadenopathy.',
						status: '',
						category: 'Lymph Nodes'
					},
					{
						id: '3fb01669-b03a-4c33-a32a-35182c2ca47b',
						notes: 'No free abdominal fluid demonstrated.',
						status: '',
						category: 'Peritoneum'
					}
				]
			},
			assessment: { list: [], notes: '' },
			subjective: '',
			attachments: { list: [] },
			prescription: { list: [], notes: '' },
			invoiceAmount: 0
		};

		await this.appointmentDetailsRepository.save({
			appointmentId: createdAppointment.id,
			details: defaultDetails
		});

		this.logger.log('Created impromptu appointment', {
			appointmentId: createdAppointment.id,
			type: data.type,
			status: data.status
		});

		return createdAppointment;
	}

	/**
	 * Generate default treatment details structure for new appointments
	 * This initializes the appointment details with the standard SOAP format
	 * and includes long-term medications in the prescription list
	 */
	private async generateDefaultTreatmentDetails(
		appointmentId: string,
		patientId?: string
	) {
		// Physical exam categories - basic structure
		const physicalExamCategories = [
			'Oral cavity/Teeth',
			'Eyes/orbit',
			'Throat',
			'Respiratory',
			'Musculoskeletal',
			'Urogenital',
			'Mucous membranes',
			'Ears',
			'Cardio vascular',
			'Abdomen',
			'Glands/Lymph Nodes',
			'Rectal'
		];

		const ultrasoundExamCategories = [
			{
				category: 'Liver',
				notes: `The liver is regular in size with a homogenous echo pattern throughout the parenchyma.
				The liver margins are well-defined and smooth.
				No focal lesion or intrahepatic duct dilatation evident.`
			},
			{
				category: 'Portal Vein',
				notes: `The portal vein is patent, demonstrates hepatopetal flow with a velocity of **___ cm/s**.
				Hepatic veins demonstrate normal calibre and regular configuration.`
			},
			{
				category: 'Gallbladder',
				notes: `Normal distended gallbladder with a uniform thin wall.
				Sludge/Choleliths not seen within the dependent portion of the gallbladder.
				The common duct measurement was normal at the level of the main portal branch.`
			},
			{
				category: 'Spleen',
				notes: `Regular sized and shaped spleen with a normal homogenous parenchymal echo pattern.
				No evidence of splenic disease.`
			},

			{
				category: 'Pancreas',
				notes: `Where visualised, the pancreas is normal in size and echotexture with no evidence of pancreatic disease.`
			},
			{
				category: 'Stomach',
				notes: `The stomach is empty at the time of the scan.
				Normal wall layer architecture and gastric wall thickness (**___ mm**).
				No obvious foreign body seen within the contracted rugal folds.`
			},

			{
				category: 'Small Intestines',
				notes: `Duodenum demonstrates normal overall wall thickness (**___ mm**) and characteristic wall layer echo pattern.
				Remaining small intestines are normal with no evidence of foreign body or disease.`
			},
			{
				category: 'Colon',
				notes: `The colon contains air/faecal content/fluid at the time of scan.
				Wall layering and overall thickness appear normal.
				ICCJ identified as normal.`
			},
			{
				category: 'Kidneys',
				notes: `Both kidneys are normal in size, shape, and 
				echogenicity with cortico-medullary differentiation. 
				There is no evidence of pyelectasia or calculi. 
				Left kidney: **___ mm**, Right kidney: **___ mm** (long axis).`
			},
			{
				category: 'Adrenal Glands',
				notes: `Normal in size, echogenicity, and characteristic shape
				Left adrenal: **___ mm**, Right adrenal: **___ mm** (caudal pole).`
			},

			{
				category: 'Aorta',
				notes: `No evidence of dilation or thrombus noted.`
			},

			{
				category: 'Caudal Vena Cava (CVC)',
				notes: `No evidence of thrombus or other abnormalities.`
			},

			{
				category: 'Urinary Bladder',
				notes: `Moderately filled at time of scan.
				Smooth, uniform wall thickness within normal limits for fill status (**___ mm**).
				Trigone region did not show any abnormalities.`
			},

			{
				category: 'Prostate Gland / Testes',
				notes: `Prostate gland is normal in size, shape, and echotexture
				Bilobar measurements: **___ mm x ___ mm**.
				Prostate volume: **___ ml
				Testes: Normal shape, size, and echotexture (if intact).
				`
			},
			{
				category: 'Uterus / Ovaries',
				notes: `**Neutered/Intact status:** _______________.
				Uterus and ovaries could not be visualized.`
			},
			{
				category: 'Lymph Nodes',
				notes: `LMILN: **___ x ___ mm**, RMILN: **___ x ___ mm**.
				No gross lymphadenopathy.`
			},
			{
				category: 'Peritoneum',
				notes: `No free abdominal fluid demonstrated.`
			}
		];

		// Fetch long-term medications for the patient if patientId is provided
		let longTermMedications: any[] = [];
		if (patientId) {
			try {
				longTermMedications =
					await this.longTermMedicationsService.getLongTermMedicationsByPatientId(
						patientId
					);
			} catch (error) {
				this.logger.error(
					'Error fetching long-term medications for patient',
					{
						patientId,
						error
					}
				);
				// Continue with empty array if fetch fails
				longTermMedications = [];
			}
		}

		// Transform long-term medications to prescription format
		const prescriptionList = longTermMedications?.length
			? longTermMedications.map((medicationItem: any) => {
					return {
						id: uuidv4(),
						qty: 0,
						name: medicationItem.medication?.name ?? '',
						type: '',
						brand: '',
						dosage: '',
						comment: '',
						subList: [
							medicationItem.medication?.drug ?? '',
							medicationItem.medication?.form ?? '',
							`${medicationItem.medication?.strength ?? ''}${medicationItem.medication?.unit ?? ''}`
						],
						isLongTerm: true,
						showSubList: true,
						isRestricted: false,
						prescriptionId: medicationItem?.medication?.id,
						chargeablePrice:
							medicationItem?.medication?.chargeablePrice
					};
				})
			: [];

		return {
			appointmentId, // Inject appointmentId into details
			invoiceAmount: 0,
			subjective: '',
			objective: {
				vitals: [
					{
						time: moment().format('h:mm A'),
						weight: '',
						temperature: '',
						heartRate: '',
						respRate: '',
						attitude: '',
						painScore: '',
						mucousMembrane: '',
						capillaryRefill: '',
						bcs: '',
						bp: '',
						map: ''
					}
				],
				physicalExam: physicalExamCategories.map(category => ({
					id: uuidv4(),
					category,
					status: '',
					notes: ''
				})),
				ultrasoundExam: ultrasoundExamCategories.map(item => ({
					id: uuidv4(),
					category: item.category,
					status: '',
					notes: item.notes
				})),
				bodyMaps: [],
				labReports: []
			},
			plans: {
				list: [],
				notes: ''
			},
			assessment: {
				list: [],
				notes: ''
			},
			followup: null,
			prescription: {
				list: prescriptionList,
				notes: ''
			},
			attachments: {
				list: []
			}
		};
	}

	/**
	 * Emit socket updates based on call site ID to sync real-time changes
	 */
	private async emitSocketUpdatesForCallSite(
		appointmentId: string,
		callSiteId: string,
		appointmentDetails: any
	): Promise<void> {
		try {
			this.logger.log('Emitting socket updates for call site', {
				appointmentId,
				callSiteId
			});

			// Parse call site ID to determine what to emit
			const [baseCallSite, subCallSite] = callSiteId.split(':');

			if (baseCallSite !== 'CALL_SITE_ID_004') {
				// Only handle cart-related updates for now
				return;
			}

			// Determine what to emit based on sub call site ID
			switch (subCallSite) {
				case 'TU_72': // normal item
				case 'PDT_2017': // qty change
				case 'PDT_2018': // normal item delete
					await this.emitPlanUpdate(
						appointmentId,
						appointmentDetails,
						callSiteId
					);
					break;

				case 'TU_835': // labreport
				case 'TU_898': // idexx
				case 'TU_654': // labreport delete
					await this.emitPlanUpdate(
						appointmentId,
						appointmentDetails,
						callSiteId
					);
					await this.emitObjectiveLabReportUpdate(
						appointmentId,
						appointmentDetails,
						callSiteId
					);
					break;

				default:
					this.logger.log(
						'Unknown sub call site ID, emitting plan update only',
						{
							subCallSite
						}
					);
					break;
			}
		} catch (error) {
			this.logger.error('Error emitting socket updates', {
				appointmentId,
				callSiteId,
				error
			});
		}
	}

	/**
	 * Emit plan section updates
	 */
	private async emitPlanUpdate(
		appointmentId: string,
		appointmentDetails: any,
		callSiteId?: string
	): Promise<void> {
		const planData = appointmentDetails?.plans?.list;
		if (planData) {
			await this.appointmentGateway.publishAppointmentUpdate(
				appointmentId,
				{
					appointmentId: appointmentId,
					key: 'plans.list',
					value: planData
				}
			);

			this.logger.log('Emitted plan update', {
				appointmentId,
				callSiteId,
				plansEmitted: planData.length
			});
		} else {
			this.logger.warn('DEBUG: No plan data to emit', {
				appointmentId,
				callSiteId
			});
		}
	}

	/**
	 * Emit objective lab report updates
	 */
	private async emitObjectiveLabReportUpdate(
		appointmentId: string,
		appointmentDetails: any,
		callSiteId?: string
	): Promise<void> {
		const labReportData = appointmentDetails?.objective?.labReports;
		if (labReportData) {
			await this.appointmentGateway.publishAppointmentUpdate(
				appointmentId,
				{
					appointmentId: appointmentId,
					key: 'objective.labReports',
					value: labReportData
				}
			);
			this.logger.log('Emitted objective lab report update', {
				appointmentId,
				callSiteId,
				labReportsEmitted: labReportData.length
			});
		} else {
			this.logger.warn('DEBUG: No lab report data to emit', {
				appointmentId,
				callSiteId
			});
		}
	}

	/**
	 * Send email and WhatsApp notifications when an appointment is updated or cancelled.
	 * For now creation notifications stay where they are (createAppointment).
	 */
	private async sendAppointmentNotifications(
		appointment: AppointmentEntity,
		type: 'update' | 'cancel'
	): Promise<void> {
		try {
			// Ensure we have the necessary relations loaded – patient → owners, clinic, brand
			if (!appointment.patient || !appointment.patient.patientOwners) {
				this.logger.warn(
					'sendAppointmentNotifications called without patient owners loaded',
					{
						appointmentId: appointment.id,
						type
					}
				);
				return;
			}

			const formattedDate = moment(appointment.date).format(
				'MMMM Do YYYY'
			);
			const formattedTime = `${moment(appointment.startTime)
				.add(5, 'hours')
				.add(30, 'minute')
				.format('h:mm a')}`;

			for (const patientOwner of appointment.patient.patientOwners) {
				const ownerFirstName =
					patientOwner?.ownerBrand?.firstName || '';
				const ownerLastName = patientOwner?.ownerBrand?.lastName || '';
				const ownerMobileNumber = `${patientOwner?.ownerBrand?.globalOwner?.countryCode || ''}${
					patientOwner?.ownerBrand?.globalOwner?.phoneNumber || ''
				}`;

				const brandName = appointment?.clinic?.brand?.name;
				const contactInformation =
					appointment?.clinic?.phoneNumbers?.[0]?.number;

				/* ----------------------------- EMAIL NOTIFICATION ---------------------------- */
				let mailPayload: {
					body: string;
					subject: string;
					toMailAddress: string;
				} | null = null;
				if (type === 'update') {
					mailPayload = appointmentUpdateMailGenerator({
						firstname: ownerFirstName,
						lastName: ownerLastName,
						updatedAppointmentDate: formattedDate,
						updatedAppointmentTime: formattedTime,
						brandName,
						contactInformation,
						petName: appointment.patient.patientName,
						email: patientOwner?.ownerBrand?.email || ''
					});
				} else if (type === 'cancel') {
					mailPayload = appointmentCancelMailGenerator({
						firstname: ownerFirstName,
						lastName: ownerLastName,
						appointmentDate: formattedDate,
						appointmentTime: formattedTime,
						brandName,
						contactInformation,
						email: patientOwner?.ownerBrand?.email || ''
					});
				}

				if (mailPayload && mailPayload.toMailAddress) {
					if (isProduction()) {
						this.mailService.sendMail(mailPayload);
					} else if (!isProduction()) {
						this.mailService.sendMail({
							...mailPayload,
							toMailAddress: DEV_SES_EMAIL
						});
					}
				}

				/* --------------------------- WHATSAPP NOTIFICATION --------------------------- */
				if (ownerMobileNumber) {
					// Build template args common to both templates
					const templateArgs: any = {
						clientName: `${ownerFirstName} ${ownerLastName}`.trim(),
						appointmentDate: formattedDate,
						appointmentTime: formattedTime,
						brandName,
						contactInformation,
						mobileNumber: ownerMobileNumber,
						petName: appointment.patient.patientName
					};

					let templateDataFn;
					let templateDataClinicLinkFn;
					if (type === 'update') {
						templateDataFn = getAppointmentUpdateTemplateData;
						templateDataClinicLinkFn =
							getAppointmentUpdateClinicLinkTemplateData;
					} else {
						templateDataFn = getAppointmentCancellationTemplateData;
						templateDataClinicLinkFn =
							getAppointmentCancellationClinicLinkTemplateData;
					}

					const { mobileNumber, templateName, valuesArray } =
						selectTemplate(
							appointment.clinic,
							templateArgs,
							templateDataFn,
							templateDataClinicLinkFn
						);

					if (isProductionOrUat()) {
						try {
							await this.whatsappService.sendTemplateMessage({
								templateName,
								valuesArray,
								mobileNumber
							});
						} catch (err) {
							this.logger.error(
								'Error sending WhatsApp message',
								{ err }
							);
						}
					}
				}
			}
		} catch (error) {
			this.logger.error('Error in sendAppointmentNotifications', {
				appointmentId: appointment.id,
				type,
				error
			});
		}
	}
}

function updateTimestamps(
	appointment: AppointmentEntity,
	newStatus: EnumAppointmentStatus
) {
	const currentTime = new Date();

	if (
		newStatus === EnumAppointmentStatus.Checkedin &&
		!appointment.checkinTime
	) {
		appointment.checkinTime = currentTime;
	}

	if (
		newStatus === EnumAppointmentStatus.ReceivingCare &&
		!appointment.receivingCareTime
	) {
		appointment.receivingCareTime = currentTime;
	}

	if (
		newStatus === EnumAppointmentStatus.Checkedout &&
		!appointment.checkoutTime
	) {
		appointment.checkoutTime = currentTime;
	}

	return appointment;
}
