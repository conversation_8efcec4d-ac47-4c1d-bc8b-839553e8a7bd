const puppeteer = require('puppeteer');

export const generatePDF = async (html: any) => {
	const isProd = process.env.NODE_ENV !== 'development';
	console.log(isProd);

	const browser = await puppeteer.launch({
		// ...(!isProd && {
		// 	executablePath:
		// 		'/Applications/Google Chrome.app/Contents/MacOS/Google Chrome' // Path to Chrome
		// }),
		args: [
			'--disable-gpu',
			'--disable-dev-shm-usage',
			'--disable-setuid-sandbox',
			'--no-sandbox'
		]
	});

	// // Create a new page
	const page = await browser.newPage();

	await page.setContent(html, { waitUntil: 'networkidle0' });
	await page.emulateMediaType('screen');

	const pdfBuffer = await page.pdf({
		// margin: { top: '100px', right: '50px', bottom: '100px', left: '50px' },
		printBackground: true,
		format: 'A4'
	});

	await page.close();
	await browser.close();

	return pdfBuffer;
};
