{
	"extends": "./node_modules/gts/tsconfig-google.json",
	"compilerOptions": {
		"rootDir": ".",
		"outDir": "build",
		// "experimentalDecorators": true /* Allows decorators" */,
		"resolveJsonModule": true /* Allows importing modules with a .json extension */,
		"emitDecoratorMetadata": true,
		"experimentalDecorators": true,
		"lib": ["dom", "dom.iterable", "esnext"],
		"typeRoots": ["./node_modules/@types", "./src/typings"]
	},
	"include": ["src/**/*.ts", "test/**/*.ts"],
	"exclude": [
		"node_modules",
		"coverage"
	]
}
