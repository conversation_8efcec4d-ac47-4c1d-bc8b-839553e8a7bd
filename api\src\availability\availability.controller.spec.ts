import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { HttpException, HttpStatus } from '@nestjs/common';
import { AvailabilityController } from './availability.controller';
import { AvailabilityService } from './availability.service';
import { ClinicUser } from '../clinics/entities/clinic-user.entity';

describe('AvailabilityController', () => {
    let controller: AvailabilityController;
    let availabilityService: jest.Mocked<AvailabilityService>;
    let clinicUserRepository: jest.Mocked<Repository<ClinicUser>>;

    const mockClinicUser: ClinicUser = {
        id: 'user-123',
        clinicId: 'clinic-123',
        userId: 'user-456',
        workingHours: {
            workingHours: {
                monday: [
                    {
                        isWorkingDay: true,
                        startTime: '09:00:00',
                        endTime: '17:00:00'
                    }
                ]
            }
        }
    } as ClinicUser;

    const mockClinicUsers: ClinicUser[] = [
        mockClinicUser,
        {
            ...mockClinicUser,
            id: 'user-124',
            userId: 'user-457'
        } as ClinicUser
    ];

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            controllers: [AvailabilityController],
            providers: [
                {
                    provide: AvailabilityService,
                    useValue: {
                        generateSlotsForUser: jest.fn()
                    }
                },
                {
                    provide: getRepositoryToken(ClinicUser),
                    useValue: {
                        find: jest.fn(),
                        findOne: jest.fn()
                    }
                }
            ]
        }).compile();

        controller = module.get<AvailabilityController>(AvailabilityController);
        availabilityService = module.get(AvailabilityService);
        clinicUserRepository = module.get(getRepositoryToken(ClinicUser));
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should be defined', () => {
        expect(controller).toBeDefined();
    });

    describe('initializeSingleUserSlots', () => {
        it('should be defined', () => {
            expect(controller.initializeSingleUserSlots).toBeDefined();
        });

        it('should initialize slots for a single user successfully', async () => {
            const userId = 'user-123';
            availabilityService.generateSlotsForUser.mockResolvedValue(undefined);

            const result = await controller.initializeSingleUserSlots(userId);

            expect(availabilityService.generateSlotsForUser).toHaveBeenCalledWith(
                userId,
                expect.any(Date),
                expect.any(Date)
            );
            expect(result).toEqual({ success: true });
        });

        it('should handle service errors and return error response', async () => {
            const userId = 'user-123';
            const serviceError = new Error('Database connection failed');
            availabilityService.generateSlotsForUser.mockRejectedValue(serviceError);

            const result = await controller.initializeSingleUserSlots(userId);

            expect(result).toEqual({
                success: false,
                error: 'Database connection failed'
            });
        });

        it('should handle unknown errors gracefully', async () => {
            const userId = 'user-123';
            availabilityService.generateSlotsForUser.mockRejectedValue(null);

            const result = await controller.initializeSingleUserSlots(userId);

            expect(result).toEqual({
                success: false,
                error: 'Unknown error'
            });
        });

        it('should set correct date range (90 days from today)', async () => {
            const userId = 'user-123';
            const startDate = new Date();
            const expectedEndDate = new Date();
            expectedEndDate.setDate(startDate.getDate() + 90);

            availabilityService.generateSlotsForUser.mockResolvedValue(undefined);

            await controller.initializeSingleUserSlots(userId);

            const callArgs = availabilityService.generateSlotsForUser.mock.calls[0];
            const actualStartDate = callArgs[1] as Date;
            const actualEndDate = callArgs[2] as Date;

            // Allow for small time differences due to test execution time
            expect(Math.abs(actualStartDate.getTime() - startDate.getTime())).toBeLessThan(1000);
            expect(Math.abs(actualEndDate.getTime() - expectedEndDate.getTime())).toBeLessThan(1000);
        });
    });

    describe('initializeAllSlots', () => {
        it('should be defined', () => {
            expect(controller.initializeAllSlots).toBeDefined();
        });

        it('should initialize slots for all users successfully', async () => {
            clinicUserRepository.find.mockResolvedValue(mockClinicUsers);
            availabilityService.generateSlotsForUser.mockResolvedValue(undefined);

            const result = await controller.initializeAllSlots();

            expect(clinicUserRepository.find).toHaveBeenCalled();
            expect(availabilityService.generateSlotsForUser).toHaveBeenCalledTimes(2);
            expect(result).toEqual({
                message: 'Availability slots initialization completed',
                results: {
                    total: 2,
                    successful: 2,
                    failed: 0,
                    failedUserIds: []
                }
            });
        });

        it('should handle empty clinic users list', async () => {
            clinicUserRepository.find.mockResolvedValue([]);

            const result = await controller.initializeAllSlots();

            expect(clinicUserRepository.find).toHaveBeenCalled();
            expect(availabilityService.generateSlotsForUser).not.toHaveBeenCalled();
            expect(result).toEqual({
                message: 'Availability slots initialization completed',
                results: {
                    total: 0,
                    successful: 0,
                    failed: 0,
                    failedUserIds: []
                }
            });
        });

        it('should handle partial failures during batch processing', async () => {
            clinicUserRepository.find.mockResolvedValue(mockClinicUsers);
            availabilityService.generateSlotsForUser
                .mockResolvedValueOnce(undefined) // First user succeeds
                .mockRejectedValueOnce(new Error('Service error')); // Second user fails

            const result = await controller.initializeAllSlots();

            expect(availabilityService.generateSlotsForUser).toHaveBeenCalledTimes(2);
            expect(result).toEqual({
                message: 'Availability slots initialization completed',
                results: {
                    total: 2,
                    successful: 1,
                    failed: 1,
                    failedUserIds: ['user-124']
                }
            });
        });

        it('should process users in batches of 10', async () => {
            // Create 15 mock users to test batch processing
            const manyUsers = Array.from({ length: 15 }, (_, i) => ({
                ...mockClinicUser,
                id: `user-${i}`,
                userId: `user-${i + 100}`
            })) as ClinicUser[];

            clinicUserRepository.find.mockResolvedValue(manyUsers);
            availabilityService.generateSlotsForUser.mockResolvedValue(undefined);

            const result = await controller.initializeAllSlots();

            expect(availabilityService.generateSlotsForUser).toHaveBeenCalledTimes(15);
            expect(result.results.total).toBe(15);
            expect(result.results.successful).toBe(15);
        });

        it('should handle database errors when fetching clinic users', async () => {
            const dbError = new Error('Database connection failed');
            clinicUserRepository.find.mockRejectedValue(dbError);

            await expect(controller.initializeAllSlots()).rejects.toThrow('Database connection failed');
        });

        it('should set correct date range for all users', async () => {
            const startDate = new Date();
            const expectedEndDate = new Date();
            expectedEndDate.setDate(startDate.getDate() + 90);

            clinicUserRepository.find.mockResolvedValue([mockClinicUser]);
            availabilityService.generateSlotsForUser.mockResolvedValue(undefined);

            await controller.initializeAllSlots();

            const callArgs = availabilityService.generateSlotsForUser.mock.calls[0];
            const actualStartDate = callArgs[1] as Date;
            const actualEndDate = callArgs[2] as Date;

            // Allow for small time differences due to test execution time
            expect(Math.abs(actualStartDate.getTime() - startDate.getTime())).toBeLessThan(1000);
            expect(Math.abs(actualEndDate.getTime() - expectedEndDate.getTime())).toBeLessThan(1000);
        });

        it('should handle errors with stack traces', async () => {
            const errorWithStack = new Error('Service error');
            errorWithStack.stack = 'Error: Service error\n    at test';

            clinicUserRepository.find.mockResolvedValue([mockClinicUser]);
            availabilityService.generateSlotsForUser.mockRejectedValue(errorWithStack);

            const result = await controller.initializeAllSlots();

            expect(result.results.failed).toBe(1);
            expect(result.results.failedUserIds).toContain('user-123');
        });

        it('should handle errors without stack traces', async () => {
            const errorWithoutStack = new Error('Service error');
            delete errorWithoutStack.stack;

            clinicUserRepository.find.mockResolvedValue([mockClinicUser]);
            availabilityService.generateSlotsForUser.mockRejectedValue(errorWithoutStack);

            const result = await controller.initializeAllSlots();

            expect(result.results.failed).toBe(1);
            expect(result.results.failedUserIds).toContain('user-123');
        });
    });
});