import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { Injectable, LoggerService, Optional } from '@nestjs/common';
import moment = require('moment');
import { Between, Repository, Raw, LessThan } from 'typeorm';
import { PatientReminder } from '../../patient-reminders/entities/patient-reminder.entity';
import { TraceContext } from '../middlewares/trace.context';
import { SqsService } from '../aws/sqs/sqs.service';
import { InjectRepository } from '@nestjs/typeorm';
import { DEV_SES_EMAIL } from '../constants';
import { isProduction, isProductionOrUat } from '../common/get-login-url';
import { appointmentReminderMailGenerator } from '../mail-generator/mail-template-generator';
import { AppointmentEntity } from '../../appointments/entities/appointment.entity';
import {
	getAppointmentReminderTemplateData,
	getAppointmentReminderClinicLinkTemplateData
} from '../communicatoins/whatsapp-template-generator';
import { WhatsappService } from '../whatsapp-integration/whatsapp.service';
import { SESMailService } from '../aws/ses/send-mail-service';
import { WinstonLogger } from '../logger/winston-logger.service';
import { RedisService } from '../redis/redis.service';
import { Redis, Cluster } from 'ioredis';
import { EnumAppointmentStatus } from '../../appointments/enums/enum-appointment-status';
import { selectTemplate } from '../common/template-helper.util';
import { AppointmentSessionChange } from '../../socket/appointment-session-changes.entity';
import { AppointmentSessions } from '../../socket/appointment-sessions.entity';
import { User } from '../../users/entities/user.entity';
import { GoogleSyncStatus } from '../../users/enums/google-sync-status.enum';
import { GoogleCalendarService } from '../../google-calendar/google-calendar.service';

@Injectable()
export class CronHelperService {
	private readonly logger: LoggerService;

	constructor(
		winstonLogger: WinstonLogger,
		@InjectRepository(PatientReminder)
		private readonly reminderRepository: Repository<PatientReminder>,
		@Optional() private readonly sqsService: SqsService,
		@InjectRepository(AppointmentEntity)
		private appointmentRepository: Repository<AppointmentEntity>,
		@InjectRepository(AppointmentSessionChange)
		private readonly sessionChangeRepository: Repository<AppointmentSessionChange>,
		@InjectRepository(AppointmentSessions)
		private readonly appointmentSessionsRepository: Repository<AppointmentSessions>,
		@InjectRepository(User)
		private readonly userRepository: Repository<User>,
		private readonly mailService: SESMailService,
		private readonly whatsappService: WhatsappService,
		private readonly redisService: RedisService,
		private readonly googleCalendarService: GoogleCalendarService
	) {
		// Create a service-specific logger
		this.logger = winstonLogger.createServiceLogger('CronHelperService');
	}

	// Helper method to get Redis client
	private getRedisClient(): Redis | Cluster {
		return this.redisService.getClient();
	}

	// Helper method to update reminder tracking data
	private async updateReminderTracking(
		appointmentId: string,
		reminderTracking: any
	): Promise<void> {
		await this.appointmentRepository.update(appointmentId, {
			reminderTracking
		});
	}

	// Helper method to check if all owner notifications have been sent
	private allOwnersNotified(
		appointment: AppointmentEntity,
		notificationType: 'email' | 'whatsapp'
	): boolean {
		// If no reminder tracking exists yet, we need to send notifications
		if (!appointment.reminderTracking?.ownerNotifications) {
			return false;
		}

		// Check if all owners have received notifications
		const owners = appointment?.patient?.patientOwners || [];
		if (owners.length === 0) {
			return false;
		}

		const statusField =
			notificationType === 'email' ? 'emailStatus' : 'whatsappStatus';

		// Check each owner has received notification
		return owners.every(owner => {
			const ownerId = owner?.ownerBrand?.id;
			if (!ownerId) return false;

			const ownerTracking =
				appointment.reminderTracking?.ownerNotifications?.[ownerId];
			return ownerTracking && ownerTracking[statusField] === 'sent';
		});
	}

	@Cron(CronExpression.EVERY_HOUR)
	async sendReminderNotifications() {
		const lockKey = 'reminder_cron_lock';
		const timestamp = new Date().toISOString();
		let lockAcquired = false;

		try {
			// Log the start of the process with timestamp
			this.logger.log(
				`Starting sendReminderNotifications at ${timestamp}`
			);

			// Check current lock TTL
			const currentTtl = await this.redisService.getTtl(lockKey);
			this.logger.log(
				`Current lock TTL for patient reminders: ${currentTtl} seconds`,
				{
					lockKey,
					timestamp,
					ttl: currentTtl
				}
			);

			// If TTL is suspiciously high or there's a stale lock, force clear it
			if (currentTtl > 55 * 60) {
				this.logger.log(
					`Detected incorrect TTL (${currentTtl}s). Forcing expiration...`,
					{
						lockKey,
						timestamp
					}
				);
				await this.getRedisClient().del(lockKey);
				this.logger.log(
					`Forced lock deletion complete for ${lockKey}`,
					{
						lockKey,
						timestamp
					}
				);
			}

			// Also clear lock if it's close to expiring but still exists
			if (currentTtl > 0 && currentTtl < 300) {
				// If less than 5 minutes remaining
				this.logger.log(
					`Lock about to expire (${currentTtl}s remaining). Cleaning up proactively...`,
					{
						lockKey,
						timestamp
					}
				);
				await this.getRedisClient().del(lockKey);
				this.logger.log(
					`Proactive lock cleanup complete for ${lockKey}`,
					{
						lockKey,
						timestamp
					}
				);
			}

			// Attempt to acquire lock
			const lockExpiry = 30 * 60; // 30 minutes
			const lock = await this.redisService.setLock(
				lockKey,
				'locked',
				lockExpiry
			);

			if (!lock) {
				this.logger.log(
					`Skipping execution. Lock key ${lockKey} still exists.`,
					{
						lockKey,
						timestamp
					}
				);
				return;
			}

			lockAcquired = true;

			this.logger.log(
				`Lock acquired successfully for ${lockKey}. TTL: ${lockExpiry}s. Proceeding with cron job...`,
				{
					lockKey,
					timestamp,
					lockTtl: lockExpiry
				}
			);

			const now = moment().add(15, 'hours').startOf('hour').toDate();
			const nextHour = moment().add(15, 'hours').endOf('hour').toDate();

			const reminders = await this.reminderRepository.find({
				where: { dueDate: Between(now, nextHour) },
				relations: [
					'patient',
					'patient.patientOwners',
					'patient.patientOwners.ownerBrand',
					'patient.patientOwners.ownerBrand.globalOwner',
					'clinic',
					'clinic.brand'
				]
			});

			// Filter out reminders for deceased patients
			const activeReminders = reminders.filter(
				reminder => !reminder.patient?.isDeceased
			);

			if (reminders.length !== activeReminders.length) {
				const skippedPatients = reminders
					.filter(reminder => reminder.patient?.isDeceased)
					.map(reminder => ({
						patientId: reminder.patient?.id,
						reminderId: reminder.id
					}));

				this.logger.log(
					`Filtered out ${reminders.length - activeReminders.length} reminders for deceased patients`,
					{
						skippedPatients,
						totalSkipped: skippedPatients.length,
						timestamp: new Date().toISOString()
					}
				);
			}

			if (activeReminders.length === 0) {
				this.logger.log('No reminders to send for the next day.');
				return;
			}

			this.logger.log(
				`total no. of reminders:=> ${activeReminders.length}.`
			);

			// Create minimal reminder info for logging
			const reminderIds = activeReminders.map(reminder => ({
				reminderId: reminder.id,
				patientId: reminder.patient?.id
			}));

			for (let i = 0; i < activeReminders.length; i += 1) {
				this.sqsService.sendMessage({
					queueKey: 'NidanaSendDocuments',
					messageBody: {
						traceID: TraceContext.getTraceId(),
						data: {
							reminders: [activeReminders[i]],
							serviceType: 'sendReminderNotification'
						}
					},
					deduplicationId: activeReminders[i].id
				});

				this.logger.log(
					`Sent sqs message for ${activeReminders[i].id} of reminders.`
				);
			}

			this.logger.log(
				`Sent ${activeReminders.length} notifications for reminders due tomorrow.`,
				{
					timestamp: new Date().toISOString(),
					reminderIds
				}
			);
		} catch (error: any) {
			// Enhanced error logging with more specific information
			this.logger.log(
				'CronHelperService ~ sendReminderNotifications ~ error:',
				{
					error: error.message || error,
					stack: error.stack,
					errorName: error.name,
					errorCode: error.code,
					timestamp: new Date().toISOString()
				}
			);
		} finally {
			// Always release the lock if we acquired it
			if (lockAcquired) {
				try {
					await this.getRedisClient().del(lockKey);
					this.logger.log(
						`Lock ${lockKey} released after execution`,
						{
							lockKey,
							timestamp: new Date().toISOString()
						}
					);
				} catch (redisError: any) {
					this.logger.log(
						`Failed to release lock ${lockKey} after execution:`,
						{
							error: redisError.message || redisError,
							stack: redisError.stack,
							lockKey,
							timestamp: new Date().toISOString()
						}
					);
				}
			}
		}
	}

	@Cron(CronExpression.EVERY_HOUR)
	async sendAppointmentMailBy24thHour() {
		const lockKey = 'upcoming_appointment_reminder_cron_lock';
		const timestamp = new Date().toISOString();
		let lockAcquired = false;
		// Track minimal notification info for logging
		const notificationsTracking = {
			appointments: [] as { id: string; patientId: string }[],
			emails: [] as {
				appointmentId: string;
				patientId: string;
				ownerId: string;
			}[],
			whatsapp: [] as {
				appointmentId: string;
				patientId: string;
				ownerId: string;
			}[]
		};

		try {
			// Log the start of the process with timestamp
			this.logger.log(
				`Starting sendAppointmentMailBy24thHour at ${timestamp}`
			);

			// Check current lock TTL
			const currentTtl = await this.redisService.getTtl(lockKey);
			this.logger.log(
				`Current lock TTL for appointment reminders: ${currentTtl} seconds`,
				{
					lockKey,
					timestamp,
					ttl: currentTtl
				}
			);

			// If TTL is suspiciously high or there's a stale lock, force clear it
			if (currentTtl > 55 * 60) {
				this.logger.log(
					`Detected incorrect TTL (${currentTtl}s). Forcing expiration...`,
					{
						lockKey,
						timestamp
					}
				);
				await this.getRedisClient().del(lockKey);
				this.logger.log(
					`Forced lock deletion complete for ${lockKey}`,
					{
						lockKey,
						timestamp
					}
				);
			}

			// Also clear lock if it's close to expiring but still exists
			if (currentTtl > 0 && currentTtl < 300) {
				// If less than 5 minutes remaining
				this.logger.log(
					`Lock about to expire (${currentTtl}s remaining). Cleaning up proactively...`,
					{
						lockKey,
						timestamp
					}
				);
				await this.getRedisClient().del(lockKey);
				this.logger.log(
					`Proactive lock cleanup complete for ${lockKey}`,
					{
						lockKey,
						timestamp
					}
				);
			}

			// Attempt to acquire lock
			const lockExpiry = 30 * 60; // 30 minutes
			const lock = await this.redisService.setLock(
				lockKey,
				'locked',
				lockExpiry
			);

			if (!lock) {
				this.logger.log(
					`Skipping execution. Lock key ${lockKey} still exists.`,
					{
						lockKey,
						timestamp
					}
				);
				return;
			}

			lockAcquired = true;

			this.logger.log(
				`Lock acquired successfully for ${lockKey}. TTL: ${lockExpiry}s. Proceeding with cron job...`,
				{
					lockKey,
					timestamp,
					lockTtl: lockExpiry
				}
			);

			// Calculate time windows for reminders
			const now = moment();

			// 1. Upcoming appointments (next 16 hours window)
			const upcomingStart = now.clone().add(16, 'hours').startOf('hour');
			const upcomingEnd = now.clone().add(16, 'hours').endOf('hour');

			this.logger.log(
				// Updated log message to reflect only upcoming window
				`Reminder window: Upcoming ${upcomingStart.format('YYYY-MM-DD HH:mm')} to ${upcomingEnd.format('YYYY-MM-DD HH:mm')}`,
				{ timestamp }
			);

			// Query for upcoming appointments using QueryBuilder to combine date and time fields
			// Renamed from upcomingAppointments to appointments as it's the only query now
			const appointments = await this.appointmentRepository
				.createQueryBuilder('appointment')
				.leftJoinAndSelect(
					'appointment.appointmentDoctors',
					'appointmentDoctors'
				)
				.leftJoinAndSelect(
					'appointmentDoctors.clinicUser',
					'clinicUser'
				)
				.leftJoinAndSelect('clinicUser.user', 'user')
				.leftJoinAndSelect('appointment.patient', 'patient')
				.leftJoinAndSelect('patient.patientOwners', 'patientOwners')
				.leftJoinAndSelect('patientOwners.ownerBrand', 'ownerBrand')
				.leftJoinAndSelect('ownerBrand.globalOwner', 'globalOwner')
				.leftJoinAndSelect('appointment.clinic', 'clinic')
				.leftJoinAndSelect('clinic.brand', 'brand')
				.where('appointment.status = :status', {
					status: EnumAppointmentStatus.Scheduled
				})
				// Add condition to filter out deleted appointments
				.andWhere('appointment.deleted_at IS NULL')
				// Combine date and time for proper comparison
				.andWhere(
					`
					make_timestamp(
						EXTRACT(YEAR FROM appointment.date)::int,
						EXTRACT(MONTH FROM appointment.date)::int,
						EXTRACT(DAY FROM appointment.date)::int,
						EXTRACT(HOUR FROM appointment.start_time)::int,
						EXTRACT(MINUTE FROM appointment.start_time)::int,
						EXTRACT(SECOND FROM appointment.start_time)::double precision
					) BETWEEN :upcomingStart AND :upcomingEnd
				`,
					{
						// Pass Date objects as parameters
						upcomingStart: now.toDate(),
						upcomingEnd: upcomingEnd.toDate()
					}
				)
				// Added retry logic directly to this query
				.andWhere(
					`(
					  appointment.reminder_tracking IS NULL OR
					  COALESCE((appointment.reminder_tracking->>'retryCount')::int, 0) < 3
					)`
				)
				.getMany();

			// Updated log message to reflect only the single query result
			this.logger.log(
				`Found ${appointments.length} upcoming appointments (including retries) to process`,
				{
					// Removed missedCount
					upcomingCount: appointments.length,
					timestamp
				}
			);

			// Track appointment IDs for minimal logging
			for (const appointment of appointments) {
				// Skip appointments where all owners have been notified already
				const allEmailsSent = this.allOwnersNotified(
					appointment,
					'email'
				);
				const allWhatsappSent = this.allOwnersNotified(
					appointment,
					'whatsapp'
				);

				if (allEmailsSent && allWhatsappSent) {
					this.logger.log(
						`Skipping appointment ${appointment.id} - all notifications already sent`,
						{
							appointmentId: appointment.id,
							timestamp: new Date().toISOString()
						}
					);
					continue;
				}

				// Initialize or update reminderTracking
				const currentTracking = appointment.reminderTracking || {};
				const retryCount = currentTracking.retryCount || 0;
				const reminderTracking = {
					...currentTracking,
					lastProcessedAt: new Date().toISOString(),
					retryCount: retryCount + 1,
					ownerNotifications: currentTracking.ownerNotifications || {}
				};

				// Update tracking before sending to prevent duplicates on retries
				await this.updateReminderTracking(
					appointment.id,
					reminderTracking
				);

				notificationsTracking.appointments.push({
					id: appointment.id,
					patientId: appointment.patient?.id
				});

				appointment?.patient?.patientOwners.forEach(
					async (patientOwner: any) => {
						// Use ownerBrand branch for appointment reminders
						const ownerFirstName =
							patientOwner?.ownerBrand?.firstName || '';
						const ownerLastName =
							patientOwner?.ownerBrand?.lastName || '';
						const ownerMobileNumber = `${patientOwner?.ownerBrand?.globalOwner?.countryCode || ''}${patientOwner?.ownerBrand?.globalOwner?.phoneNumber || ''}`;
						const ownerId = patientOwner?.ownerBrand?.id;

						// Initialize owner tracking if not exists
						if (!reminderTracking.ownerNotifications[ownerId]) {
							reminderTracking.ownerNotifications[ownerId] = {};
						}

						// Skip email if already successfully sent to this owner
						if (
							patientOwner?.ownerBrand?.email &&
							!(
								reminderTracking.ownerNotifications[ownerId]
									?.emailStatus === 'sent'
							)
						) {
							try {
								const { body, subject, toMailAddress } =
									appointmentReminderMailGenerator({
										brandName:
											appointment?.clinic?.brand?.name,
										contactInformation:
											appointment?.clinic
												?.phoneNumbers?.[0]?.number ||
											'provided contact no.',
										petName:
											appointment?.patient?.patientName,
										email: patientOwner?.ownerBrand?.email,
										firstname: ownerFirstName,
										lastName: ownerLastName,
										appointmentdate: moment(
											appointment.date
										).format('MMMM Do YYYY'),
										appointmentTime: `${moment(appointment.startTime).add(5, 'hours').add(30, 'minute').format('h:mm a')}`
									});

								// Track email recipients separately
								notificationsTracking.emails.push({
									appointmentId: appointment.id,
									patientId: appointment.patient?.id,
									ownerId: patientOwner.ownerBrand?.id
								});

								if (isProduction()) {
									this.mailService.sendMail({
										body,
										subject,
										toMailAddress
									});
								} else if (!isProduction()) {
									this.mailService.sendMail({
										body,
										subject,
										toMailAddress: DEV_SES_EMAIL
									});
								}

								// Update email status to sent for this owner
								reminderTracking.ownerNotifications[ownerId] = {
									...reminderTracking.ownerNotifications[
										ownerId
									],
									emailStatus: 'sent' as const,
									emailSentAt: new Date().toISOString()
								};

								// Also update top-level status for backwards compatibility
								reminderTracking.emailStatus = 'sent';
								reminderTracking.emailSentAt =
									new Date().toISOString();

								await this.updateReminderTracking(
									appointment.id,
									reminderTracking
								);
							} catch (error: any) {
								// Log error and update status for this owner
								this.logger.log(
									`Failed to send email reminder for appointment ${appointment.id} to owner ${ownerId}`,
									{
										error: error.message || String(error),
										appointmentId: appointment.id,
										ownerId
									}
								);

								reminderTracking.ownerNotifications[ownerId] = {
									...reminderTracking.ownerNotifications[
										ownerId
									],
									emailStatus: 'failed' as const,
									emailError: error.message || String(error)
								};

								await this.updateReminderTracking(
									appointment.id,
									reminderTracking
								);
							}
						}

						// Skip WhatsApp if already successfully sent to this owner
						if (
							ownerMobileNumber &&
							!(
								reminderTracking.ownerNotifications[ownerId]
									?.whatsappStatus === 'sent'
							) &&
							isProductionOrUat()
						) {
							try {
								const templateArgs = {
									appointmentDate: moment(
										appointment?.date
									).format('MMMM Do YYYY'),
									appointmentTime: `${moment(appointment?.startTime).add(5, 'hours').add(30, 'minute').format('h:mm a')}`,
									brandName: appointment?.clinic?.brand?.name,
									contactInformation:
										appointment?.clinic?.phoneNumbers?.[0]
											?.number || '',
									clientName: `${ownerFirstName} ${ownerLastName}`,
									mobileNumber: ownerMobileNumber,
									petName: appointment.patient.patientName
								};

								// Use the selectTemplate utility function to choose appropriate template
								const {
									mobileNumber,
									templateName,
									valuesArray
								} = selectTemplate(
									appointment?.clinic,
									templateArgs,
									getAppointmentReminderTemplateData,
									getAppointmentReminderClinicLinkTemplateData
								);

								this.whatsappService.sendTemplateMessage({
									templateName,
									valuesArray,
									mobileNumber
								});

								// Track WhatsApp recipients separately
								notificationsTracking.whatsapp.push({
									appointmentId: appointment.id,
									patientId: appointment.patient?.id,
									ownerId: patientOwner.ownerBrand?.id
								});

								// Update WhatsApp status to sent for this owner
								reminderTracking.ownerNotifications[ownerId] = {
									...reminderTracking.ownerNotifications[
										ownerId
									],
									whatsappStatus: 'sent' as const,
									whatsappSentAt: new Date().toISOString()
								};

								// Also update top-level status for backwards compatibility
								reminderTracking.whatsappStatus = 'sent';
								reminderTracking.whatsappSentAt =
									new Date().toISOString();

								await this.updateReminderTracking(
									appointment.id,
									reminderTracking
								);
							} catch (err: any) {
								// Log error and update status for this owner
								this.logger.log(
									`Error in sending WhatsApp message for appointment ${appointment.id} to owner ${ownerId}`,
									{
										error: err.message || String(err),
										appointmentId: appointment.id,
										ownerId
									}
								);

								reminderTracking.ownerNotifications[ownerId] = {
									...reminderTracking.ownerNotifications[
										ownerId
									],
									whatsappStatus: 'failed' as const,
									whatsappError: err.message || String(err)
								};

								await this.updateReminderTracking(
									appointment.id,
									reminderTracking
								);
							}
						}
					}
				);
			}

			// Simplified log with minimal information
			this.logger.log(
				`Appointment reminders processed: ${notificationsTracking.appointments.length} appointments, ${notificationsTracking.emails.length} emails, ${notificationsTracking.whatsapp.length} WhatsApp messages`,
				{
					timestamp: new Date().toISOString(),
					appointmentIds: notificationsTracking.appointments,
					emailNotifications: notificationsTracking.emails,
					whatsappNotifications: notificationsTracking.whatsapp
				}
			);
		} catch (error: any) {
			// Enhanced error logging with more specific information
			this.logger.log(
				'CronHelperService ~ sendAppointmentMailBy24thHour ~ error:',
				{
					error: error.message || error,
					stack: error.stack,
					errorName: error.name,
					errorCode: error.code,
					timestamp: new Date().toISOString()
				}
			);
		} finally {
			// Always release the lock if we acquired it
			if (lockAcquired) {
				try {
					await this.getRedisClient().del(lockKey);
					this.logger.log(
						`Lock ${lockKey} released after execution`,
						{
							lockKey,
							timestamp: new Date().toISOString()
						}
					);
				} catch (redisError: any) {
					this.logger.log(
						`Failed to release lock ${lockKey} after execution:`,
						{
							error: redisError.message || redisError,
							stack: redisError.stack,
							lockKey,
							timestamp: new Date().toISOString()
						}
					);
				}
			}
		}
	}

	/**
	 * Schedule daily availability validation tasks
	 * Runs at 2 AM every day to minimize impact on system performance
	 */
	@Cron('0 2 * * *') // At 2 AM every day
	async scheduleAvailabilityDailyValidation() {
		const lockKey = 'availability_daily_validation_lock';

		try {
			// Try to acquire a lock (10 minute expiry)
			const lock = await this.redisService.setLock(
				lockKey,
				'locked',
				10 * 60
			);
			if (!lock) {
				this.logger.log(
					'Skipping daily availability validation - lock exists'
				);
				return;
			}

			this.logger.log('Scheduling daily availability validation tasks');

			await this.sqsService.sendMessage({
				queueKey: 'NidanaAvailabilityMaintenance',
				messageBody: {
					data: {
						taskType: 'daily_validation',
						timestamp: new Date().toISOString()
					}
				},
				deduplicationId: `availability-daily-validation-${new Date().toISOString().split('T')[0]}`
			});

			this.logger.log(
				'Successfully scheduled daily availability validation'
			);
		} catch (error) {
			this.logger.error(
				'Error scheduling daily availability validation',
				{
					error
				}
			);
			// Release lock in case of error
			await this.getRedisClient().del(lockKey);
		}
	}

	/**
	 * Schedule weekly availability defragmentation tasks
	 * Runs at 3 AM every Sunday to optimize slots for the upcoming week
	 */
	@Cron('0 3 * * 0') // At 3 AM every Sunday
	async scheduleAvailabilityWeeklyDefragmentation() {
		const lockKey = 'availability_weekly_defragmentation_lock';

		try {
			// Try to acquire a lock (20 minute expiry)
			const lock = await this.redisService.setLock(
				lockKey,
				'locked',
				20 * 60
			);
			if (!lock) {
				this.logger.log(
					'Skipping weekly availability defragmentation - lock exists'
				);
				return;
			}

			this.logger.log(
				'Scheduling weekly availability defragmentation tasks'
			);

			await this.sqsService.sendMessage({
				queueKey: 'NidanaAvailabilityMaintenance',
				messageBody: {
					data: {
						taskType: 'weekly_defragmentation',
						timestamp: new Date().toISOString()
					}
				},
				deduplicationId: `availability-weekly-defrag-${new Date().toISOString().split('T')[0]}`
			});

			this.logger.log(
				'Successfully scheduled weekly availability defragmentation'
			);
		} catch (error) {
			this.logger.error(
				'Error scheduling weekly availability defragmentation',
				{
					error
				}
			);
			// Release lock in case of error
			await this.getRedisClient().del(lockKey);
		}
	}

	/**
	 * Schedule monthly availability cleanup tasks
	 * Runs at 4 AM on the first day of each month
	 */
	@Cron('0 4 1 * *') // At 4 AM on the 1st of every month
	async scheduleAvailabilityMonthlyCleanup() {
		const lockKey = 'availability_monthly_cleanup_lock';

		try {
			// Try to acquire a lock (30 minute expiry)
			const lock = await this.redisService.setLock(
				lockKey,
				'locked',
				30 * 60
			);
			if (!lock) {
				this.logger.log(
					'Skipping monthly availability cleanup - lock exists'
				);
				return;
			}

			this.logger.log('Scheduling monthly availability cleanup tasks');

			await this.sqsService.sendMessage({
				queueKey: 'NidanaAvailabilityMaintenance',
				messageBody: {
					data: {
						taskType: 'monthly_cleanup',
						timestamp: new Date().toISOString()
					}
				},
				deduplicationId: `availability-monthly-cleanup-${new Date().toISOString().split('T')[0]}`
			});

			this.logger.log(
				'Successfully scheduled monthly availability cleanup'
			);
		} catch (error) {
			this.logger.error('Error scheduling monthly availability cleanup', {
				error
			});
			// Release lock in case of error
			await this.getRedisClient().del(lockKey);
		}
	}

	/**
	 * Cleanup stale appointment session changes
	 * Runs every 6 hours to remove session changes older than 24 hours
	 */
	@Cron('0 */6 * * *') // Every 6 hours
	async cleanupStaleAppointmentSessionChanges() {
		const lockKey = 'appointment_session_changes_cleanup_lock';
		const timestamp = new Date().toISOString();
		let lockAcquired = false;

		try {
			this.logger.log(
				`Starting cleanupStaleAppointmentSessionChanges at ${timestamp}`
			);

			// Check current lock TTL
			const currentTtl = await this.redisService.getTtl(lockKey);
			this.logger.log(
				`Current lock TTL for session changes cleanup: ${currentTtl} seconds`,
				{
					lockKey,
					timestamp,
					ttl: currentTtl
				}
			);

			// If TTL is suspiciously high or there's a stale lock, force clear it
			if (currentTtl > 25 * 60) {
				this.logger.log(
					`Detected incorrect TTL (${currentTtl}s). Forcing expiration...`,
					{
						lockKey,
						timestamp
					}
				);
				await this.getRedisClient().del(lockKey);
				this.logger.log(
					`Forced lock deletion complete for ${lockKey}`,
					{
						lockKey,
						timestamp
					}
				);
			}

			// Attempt to acquire lock
			const lockExpiry = 20 * 60; // 20 minutes
			const lock = await this.redisService.setLock(
				lockKey,
				'locked',
				lockExpiry
			);

			if (!lock) {
				this.logger.log(
					`Skipping execution. Lock key ${lockKey} still exists.`,
					{
						lockKey,
						timestamp
					}
				);
				return;
			}

			lockAcquired = true;

			this.logger.log(
				`Lock acquired successfully for ${lockKey}. TTL: ${lockExpiry}s. Proceeding with cleanup...`,
				{
					lockKey,
					timestamp,
					lockTtl: lockExpiry
				}
			);

			// Calculate cutoff time (24 hours ago)
			const cutoffTime = moment().subtract(24, 'hours').toDate();

			this.logger.log(
				`Cleaning up appointment session changes older than ${cutoffTime.toISOString()}`,
				{
					cutoffTime: cutoffTime.toISOString(),
					timestamp
				}
			);

			// Delete stale session changes
			const deleteResult = await this.sessionChangeRepository
				.createQueryBuilder()
				.delete()
				.from(AppointmentSessionChange)
				.where('updatedAt < :cutoffTime', { cutoffTime })
				.execute();

			this.logger.log(
				`Successfully cleaned up ${deleteResult.affected || 0} stale appointment session changes`,
				{
					deletedCount: deleteResult.affected || 0,
					cutoffTime: cutoffTime.toISOString(),
					timestamp: new Date().toISOString()
				}
			);
		} catch (error: any) {
			this.logger.error(
				'CronHelperService ~ cleanupStaleAppointmentSessionChanges ~ error:',
				{
					error: error.message || error,
					stack: error.stack,
					errorName: error.name,
					errorCode: error.code,
					timestamp: new Date().toISOString()
				}
			);
		} finally {
			// Always release the lock if we acquired it
			if (lockAcquired) {
				try {
					await this.getRedisClient().del(lockKey);
					this.logger.log(
						`Lock ${lockKey} released after execution`,
						{
							lockKey,
							timestamp: new Date().toISOString()
						}
					);
				} catch (redisError: any) {
					this.logger.log(
						`Failed to release lock ${lockKey} after execution:`,
						{
							error: redisError.message || redisError,
							stack: redisError.stack,
							lockKey,
							timestamp: new Date().toISOString()
						}
					);
				}
			}
		}
	}

	/**
	 * Cleanup stale appointment sessions
	 * Runs every 4 hours to remove sessions older than 12 hours
	 */
	@Cron('0 */4 * * *') // Every 4 hours
	async cleanupStaleAppointmentSessions() {
		const lockKey = 'appointment_sessions_cleanup_lock';
		const timestamp = new Date().toISOString();
		let lockAcquired = false;

		try {
			this.logger.log(
				`Starting cleanupStaleAppointmentSessions at ${timestamp}`
			);

			// Check current lock TTL
			const currentTtl = await this.redisService.getTtl(lockKey);
			this.logger.log(
				`Current lock TTL for appointment sessions cleanup: ${currentTtl} seconds`,
				{
					lockKey,
					timestamp,
					ttl: currentTtl
				}
			);

			// If TTL is suspiciously high or there's a stale lock, force clear it
			if (currentTtl > 25 * 60) {
				this.logger.log(
					`Detected incorrect TTL (${currentTtl}s). Forcing expiration...`,
					{
						lockKey,
						timestamp
					}
				);
				await this.getRedisClient().del(lockKey);
				this.logger.log(
					`Forced lock deletion complete for ${lockKey}`,
					{
						lockKey,
						timestamp
					}
				);
			}

			// Attempt to acquire lock
			const lockExpiry = 20 * 60; // 20 minutes
			const lock = await this.redisService.setLock(
				lockKey,
				'locked',
				lockExpiry
			);

			if (!lock) {
				this.logger.log(
					`Skipping execution. Lock key ${lockKey} still exists.`,
					{
						lockKey,
						timestamp
					}
				);
				return;
			}

			lockAcquired = true;

			this.logger.log(
				`Lock acquired successfully for ${lockKey}. TTL: ${lockExpiry}s. Proceeding with cleanup...`,
				{
					lockKey,
					timestamp,
					lockTtl: lockExpiry
				}
			);

			// Calculate cutoff time (12 hours ago)
			const cutoffTime = moment().subtract(12, 'hours').toDate();

			this.logger.log(
				`Cleaning up appointment sessions older than ${cutoffTime.toISOString()}`,
				{
					cutoffTime: cutoffTime.toISOString(),
					timestamp
				}
			);

			// Delete stale appointment sessions
			const deleteResult = await this.appointmentSessionsRepository
				.createQueryBuilder()
				.delete()
				.from(AppointmentSessions)
				.where('updated_at < :cutoffTime', { cutoffTime })
				.execute();

			this.logger.log(
				`Successfully cleaned up ${deleteResult.affected || 0} stale appointment sessions`,
				{
					deletedCount: deleteResult.affected || 0,
					cutoffTime: cutoffTime.toISOString(),
					timestamp: new Date().toISOString()
				}
			);
		} catch (error: any) {
			this.logger.error(
				'CronHelperService ~ cleanupStaleAppointmentSessions ~ error:',
				{
					error: error.message || error,
					stack: error.stack,
					errorName: error.name,
					errorCode: error.code,
					timestamp: new Date().toISOString()
				}
			);
		} finally {
			// Always release the lock if we acquired it
			if (lockAcquired) {
				try {
					await this.getRedisClient().del(lockKey);
					this.logger.log(
						`Lock ${lockKey} released after execution`,
						{
							lockKey,
							timestamp: new Date().toISOString()
						}
					);
				} catch (redisError: any) {
					this.logger.log(
						`Failed to release lock ${lockKey} after execution:`,
						{
							error: redisError.message || redisError,
							stack: redisError.stack,
							lockKey,
							timestamp: new Date().toISOString()
						}
					);
				}
			}
		}
	}

	/**
	 * Refresh Google Calendar caches for users whose last sync is stale (>6h).
	 * Runs every 2 hours.
	 */
	@Cron('0 */2 * * *') // every 2 hours at minute 0
	async refreshStaleGoogleCalendarCaches() {
		const threshold = moment().subtract(6, 'hours').toDate();
		try {
			const staleUsers = await this.userRepository.find({
				where: {
					isGoogleSyncEnabled: true,
					googleCalendarId: Raw(val => `${val} IS NOT NULL`),
					lastGoogleSyncAt: Raw(val => `${val} < :th`, {
						th: threshold
					}),
					googleSyncStatus: GoogleSyncStatus.SUCCESS
				},
				select: ['id']
			});

			if (staleUsers.length === 0) return;

			for (const u of staleUsers) {
				await this.sqsService.sendMessage({
					queueKey: 'NidanaGoogleCalendarSync',
					messageBody: {
						data: {
							type: 'SYNC_REFRESH',
							userId: u.id,
							timestamp: new Date().toISOString()
						}
					},
					deduplicationId: `refresh-${u.id}-${Date.now()}`
				});
			}

			this.logger.log(
				`Queued stale calendar refresh for ${staleUsers.length} users`
			);
		} catch (error) {
			this.logger.error('Failed to queue stale cache refresh', error);
		}
	}

	/**
	 * Queue incremental sync for all connected users every 10 minutes.
	 */
	@Cron('*/10 * * * *') // every 10 minutes
	async scheduleRegularGoogleSync() {
		try {
			const users = await this.userRepository.find({
				where: {
					isGoogleSyncEnabled: true,
					googleCalendarId: Raw(val => `${val} IS NOT NULL`),
					googleSyncStatus: GoogleSyncStatus.SUCCESS
				},
				select: ['id']
			});

			for (const u of users) {
				// Spread messages evenly across the 10-minute window (0-600 s)
				const randomDelay = Math.floor(Math.random() * 600);
				await this.sqsService.sendMessage({
					queueKey: 'NidanaGoogleCalendarSync',
					messageBody: {
						data: {
							type: 'INCREMENTAL_SYNC',
							userId: u.id,
							timestamp: new Date().toISOString()
						}
					},
					deduplicationId: `inc-${u.id}-${Date.now()}`,
					delaySeconds: randomDelay
				});
			}

			this.logger.log(
				`Queued incremental Google sync for ${users.length} users`
			);
		} catch (error) {
			this.logger.error('Failed to queue incremental google sync', error);
		}
	}

	/**
	 * Renew Google Calendar watch channels that will expire within the next 24 hours.
	 * Runs daily at 02:15.
	 */
	@Cron('15 2 * * *') // at 02:15 every day
	async renewGoogleWatchChannels() {
		const threshold = moment().add(24, 'hours').toDate();
		try {
			const expiringUsers = await this.userRepository.find({
				where: {
					isGoogleSyncEnabled: true,
					googleCalendarId: Raw(val => `${val} IS NOT NULL`),
					googleWebhookId: Raw(val => `${val} IS NOT NULL`),
					googleWebhookExpiresAt: LessThan(threshold)
				},
				select: ['id', 'googleCalendarId', 'googleWebhookExpiresAt']
			});

			if (expiringUsers.length === 0) return;

			for (const u of expiringUsers) {
				try {
					await this.googleCalendarService.watchCalendar(
						u.id,
						u.googleCalendarId!
					);
					this.logger.log(
						`Renewed Google watch channel for user ${u.id} (expires at ${u.googleWebhookExpiresAt})`
					);
				} catch (err: any) {
					this.logger.error(
						`Failed to renew watch channel for user ${u.id}`,
						err.stack || err
					);
				}
			}

			this.logger.log(
				`Attempted to renew watch channels for ${expiringUsers.length} users`
			);
		} catch (error) {
			this.logger.error('Failed to fetch expiring watch channels', error);
		}
	}
}
