import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { WinstonLogger } from '../../logger/winston-logger.service';
import * as AWS from 'aws-sdk';
import { PromiseResult } from 'aws-sdk/lib/request';
import { createTransport } from 'nodemailer';

@Injectable()
export class SESMailService {
	private readonly sesClient: AWS.SES;
	constructor(
		private configService: ConfigService,
		private loggerService: WinstonLogger
	) {
		this.sesClient = new AWS.SES({
			region: 'ap-south-1',
			accessKeyId: process.env.AWS_SES_ACCESS_KEY_ID,
			secretAccessKey: process.env.AWS_SES_SECRET_ACCESS_KEY
		});
	}

	async sendMail({
		body,
		subject,
		toMailAddress,
		ccMailAddress,
		pdfBuffers,
		pdfFileNames
	}: {
		toMailAddress: string;
		ccMailAddress?: string;
		subject: string;
		body: string;
		pdfBuffers?: Buffer[]; // Array of PDF buffers
		pdfFileNames?: string[]; // Array of file names for the PDFs
	}): Promise<PromiseResult<AWS.SES.SendEmailResponse, AWS.AWSError | null>> {
		return new Promise((resolve, rejects) => {
			try {
				const attachments = pdfBuffers?.map((buffer, idx) => ({
					filename: pdfFileNames?.[idx] || `Attachment_${idx}.pdf`,
					content: buffer
				}));

				const transporter = createTransport({
					SES: { ses: this.sesClient, aws: AWS }
				});
				const mailOptions = {
					from: process.env.SES_SOURCE_EMAIL,
					subject,
					html: body,
					to: toMailAddress,
					cc: ccMailAddress,
					attachments
				};
				this.loggerService.log('mail options details', {
					from: process.env.SES_SOURCE_EMAIL,
					subject,
					html: body,
					to: toMailAddress,
					cc: ccMailAddress,
				});
				transporter.sendMail(mailOptions, (err, response: any) => {
					if (err) {
						this.loggerService.error('Error sending email:', err.message);
						//return rejects(err); // Log and reject the error.
					}
					this.loggerService.log('Email Response');
					resolve(response);
				});
			} catch (err) {
				const error = {
					err,
					message: 'something went wrong during sending mail '
				};
				this.loggerService.log('Email Error', {
					err
				});
				return rejects(error);
			}
		});
	}
}
